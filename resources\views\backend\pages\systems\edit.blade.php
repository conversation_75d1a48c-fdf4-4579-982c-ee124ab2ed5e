@extends('backend.layouts.app')

@section('title', 'Edit System')

@section('content')
<!-- <PERSON> Header -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Edit System</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('systems.index') }}">Systems</a></li>
                    <li class="breadcrumb-item active">Edit System</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- System Form -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">System Information</h4>
            </div>
            <div class="card-body">
                <form action="{{ route('systems.update', $system->id) }}" method="POST" enctype="multipart/form-data" id="systemForm">
                    @csrf
                    @method('PUT')

                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Basic Information</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="title" class="form-label">System Title <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                                       id="title" name="title" value="{{ old('title', $system->title) }}" required>
                                                @error('title')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="slug" class="form-label">Slug</label>
                                                <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                                                       id="slug" name="slug" value="{{ old('slug', $system->slug) }}">
                                                <div class="form-text">Leave empty to auto-generate from title</div>
                                                @error('slug')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                                          id="description" name="description" rows="3" required>{{ old('description', $system->description) }}</textarea>
                                                <div class="form-text">Brief description for system cards and listings (max 1000 characters)</div>
                                                @error('description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="short_description" class="form-label">Short Description</label>
                                                <textarea class="form-control @error('short_description') is-invalid @enderror" 
                                                          id="short_description" name="short_description" rows="2">{{ old('short_description', $system->short_description) }}</textarea>
                                                <div class="form-text">Very brief summary for homepage display (max 500 characters)</div>
                                                @error('short_description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="full_description" class="form-label">Full Description</label>
                                                <textarea class="form-control @error('full_description') is-invalid @enderror" 
                                                          id="full_description" name="full_description" rows="8">{{ old('full_description', $system->full_description) }}</textarea>
                                                <div class="form-text">Detailed description for system detail page</div>
                                                @error('full_description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">System Settings</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                        <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                            <option value="">Select Status</option>
                                            <option value="active" {{ old('status', $system->status) == 'active' ? 'selected' : '' }}>Active</option>
                                            <option value="inactive" {{ old('status', $system->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                            <option value="development" {{ old('status', $system->status) == 'development' ? 'selected' : '' }}>In Development</option>
                                            <option value="maintenance" {{ old('status', $system->status) == 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                                        </select>
                                        @error('status')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="mb-3">
                                        <label for="category" class="form-label">Category</label>
                                        <input type="text" class="form-control @error('category') is-invalid @enderror" 
                                               id="category" name="category" value="{{ old('category', $system->category) }}">
                                        @error('category')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="featured" name="featured" value="1" 
                                                   {{ old('featured', $system->featured) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="featured">
                                                Featured System
                                            </label>
                                        </div>
                                        <div class="form-text">Featured systems appear prominently on homepage</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="version" class="form-label">Version</label>
                                        <input type="text" class="form-control @error('version') is-invalid @enderror" 
                                               id="version" name="version" value="{{ old('version', $system->version) }}" placeholder="1.0.0">
                                        @error('version')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="mb-3">
                                        <label for="launch_date" class="form-label">Launch Date</label>
                                        <input type="date" class="form-control @error('launch_date') is-invalid @enderror" 
                                               id="launch_date" name="launch_date" value="{{ old('launch_date', $system->launch_date?->format('Y-m-d')) }}">
                                        @error('launch_date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Details -->
                    <div class="row mt-4">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">System Details</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="system_url" class="form-label">System URL</label>
                                                <input type="url" class="form-control @error('system_url') is-invalid @enderror"
                                                       id="system_url" name="system_url" value="{{ old('system_url', $system->system_url) }}">
                                                @error('system_url')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="documentation_url" class="form-label">Documentation URL</label>
                                                <input type="url" class="form-control @error('documentation_url') is-invalid @enderror"
                                                       id="documentation_url" name="documentation_url" value="{{ old('documentation_url', $system->documentation_url) }}">
                                                @error('documentation_url')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="technologies_input" class="form-label">Technologies Used</label>
                                                <input type="text" class="form-control" id="technologies_input"
                                                       placeholder="Type and press Enter to add technologies">
                                                <div class="form-text">Press Enter after typing each technology</div>
                                                <div id="technologies_display" class="mt-2"></div>
                                                @error('technologies')
                                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="features_input" class="form-label">Key Features</label>
                                                <input type="text" class="form-control" id="features_input"
                                                       placeholder="Type and press Enter to add features">
                                                <div class="form-text">Press Enter after typing each feature</div>
                                                <div id="features_display" class="mt-2"></div>
                                                @error('features')
                                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="changelog" class="form-label">Changelog</label>
                                                <textarea class="form-control @error('changelog') is-invalid @enderror"
                                                          id="changelog" name="changelog" rows="4">{{ old('changelog', $system->changelog) }}</textarea>
                                                <div class="form-text">Recent changes and updates</div>
                                                @error('changelog')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">System Media</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="system_image" class="form-label">Main System Image</label>
                                        @if($system->system_image_url)
                                            <div class="mb-2">
                                                <img src="{{ $system->system_image_url }}" class="img-thumbnail" style="max-width: 200px;">
                                                <div class="form-text">Current image</div>
                                            </div>
                                        @endif
                                        <input type="file" class="form-control @error('system_image') is-invalid @enderror"
                                               id="system_image" name="system_image" accept="image/*">
                                        <div class="form-text">Upload a new image to replace current one</div>
                                        @error('system_image')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="mb-3">
                                        <label for="gallery_images" class="form-label">Gallery Images</label>
                                        @if($system->gallery_images && count($system->gallery_images) > 0)
                                            <div class="mb-2">
                                                <div class="row">
                                                    @foreach($system->gallery_images as $index => $image)
                                                        <div class="col-6 mb-2">
                                                            <div class="position-relative">
                                                                <img src="{{ Storage::url($image) }}" class="img-thumbnail w-100">
                                                                <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0"
                                                                        onclick="removeGalleryImage({{ $system->id }}, {{ $index }})">
                                                                    <i class="ri-close-line"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endif
                                        <input type="file" class="form-control @error('gallery_images') is-invalid @enderror"
                                               id="gallery_images" name="gallery_images[]" accept="image/*" multiple>
                                        <div class="form-text">Select multiple images for system gallery</div>
                                        @error('gallery_images')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SEO Settings -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">SEO Settings</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="meta_title" class="form-label">Meta Title</label>
                                                <input type="text" class="form-control @error('meta_title') is-invalid @enderror"
                                                       id="meta_title" name="meta_title" value="{{ old('meta_title', $system->meta_title) }}">
                                                <div class="form-text">Leave empty to use system title</div>
                                                @error('meta_title')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="meta_description" class="form-label">Meta Description</label>
                                                <textarea class="form-control @error('meta_description') is-invalid @enderror"
                                                          id="meta_description" name="meta_description" rows="3">{{ old('meta_description', $system->meta_description) }}</textarea>
                                                <div class="form-text">Leave empty to use short description</div>
                                                @error('meta_description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="meta_keywords" class="form-label">Meta Keywords</label>
                                                <textarea class="form-control @error('meta_keywords') is-invalid @enderror"
                                                          id="meta_keywords" name="meta_keywords" rows="3">{{ old('meta_keywords', $system->meta_keywords) }}</textarea>
                                                <div class="form-text">Comma-separated keywords</div>
                                                @error('meta_keywords')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="{{ route('systems.index') }}" class="btn btn-secondary">
                                    <i class="ri-arrow-left-line me-1"></i>Back to Systems
                                </a>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ri-save-line me-1"></i>Update System
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Technologies management
    let technologies = [];
    const technologiesInput = document.getElementById('technologies_input');
    const technologiesDisplay = document.getElementById('technologies_display');

    // Load existing technologies
    try {
        const existingTech = @json($system->technologies ?? []);
        technologies = Array.isArray(existingTech) ? existingTech : [];
        updateTechnologiesDisplay();
    } catch (e) {
        technologies = [];
    }

    // Add technology on Enter key
    technologiesInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const tech = this.value.trim();
            if (tech && !technologies.includes(tech)) {
                technologies.push(tech);
                updateTechnologiesDisplay();
                this.value = '';
            }
        }
    });

    function updateTechnologiesDisplay() {
        technologiesDisplay.innerHTML = technologies.map((tech, index) =>
            `<span class="badge bg-primary me-1 mb-1">
                ${tech}
                <button type="button" class="btn-close btn-close-white ms-1" onclick="removeTechnology(${index})"></button>
            </span>`
        ).join('');

        // Update hidden inputs
        updateHiddenInput('technologies', technologies);
    }

    // Make removeTechnology globally available
    window.removeTechnology = function(index) {
        technologies.splice(index, 1);
        updateTechnologiesDisplay();
    };

    // Features management
    let features = [];
    const featuresInput = document.getElementById('features_input');
    const featuresDisplay = document.getElementById('features_display');

    // Load existing features
    try {
        const existingFeatures = @json($system->features ?? []);
        features = Array.isArray(existingFeatures) ? existingFeatures : [];
        updateFeaturesDisplay();
    } catch (e) {
        features = [];
    }

    // Add feature on Enter key
    featuresInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const feature = this.value.trim();
            if (feature && !features.includes(feature)) {
                features.push(feature);
                updateFeaturesDisplay();
                this.value = '';
            }
        }
    });

    function updateFeaturesDisplay() {
        featuresDisplay.innerHTML = features.map((feature, index) =>
            `<span class="badge bg-success me-1 mb-1">
                ${feature}
                <button type="button" class="btn-close btn-close-white ms-1" onclick="removeFeature(${index})"></button>
            </span>`
        ).join('');

        // Update hidden inputs
        updateHiddenInput('features', features);
    }

    // Make removeFeature globally available
    window.removeFeature = function(index) {
        features.splice(index, 1);
        updateFeaturesDisplay();
    };

    // Auto-generate slug from title
    document.getElementById('title').addEventListener('input', function() {
        const slugField = document.getElementById('slug');
        if (!slugField.value) {
            slugField.value = this.value.toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/^-+|-+$/g, '');
        }
    });

    // Function to update hidden inputs for arrays
    function updateHiddenInput(name, array) {
        // Remove existing hidden inputs
        document.querySelectorAll(`input[name="${name}[]"]`).forEach(input => input.remove());

        // Add new hidden inputs
        array.forEach(item => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = `${name}[]`;
            input.value = item;
            document.getElementById('systemForm').appendChild(input);
        });
    }

    // Form validation
    document.getElementById('systemForm').addEventListener('submit', function(e) {
        const title = document.getElementById('title').value.trim();
        const description = document.getElementById('description').value.trim();
        const status = document.getElementById('status').value;

        if (!title || !description || !status) {
            e.preventDefault();
            alert('Please fill in all required fields.');
            return false;
        }
    });
});

// Remove gallery image function
function removeGalleryImage(systemId, imageIndex) {
    if (confirm('Are you sure you want to remove this image?')) {
        fetch(`/admin/systems/${systemId}/gallery/${imageIndex}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Accept': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to remove image');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to remove image');
        });
    }
}
</script>
@endsection
