<meta charset="utf-8" />
<title><?php echo $__env->yieldContent('title', 'GrandTek Admin | Modern Dashboard'); ?></title>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta content="GrandTek IT Solutions Admin Dashboard" name="description" />
<meta content="GrandTek" name="author" />
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>" />

<!-- App favicon -->
<link rel="shortcut icon" href="<?php echo e(asset('backend/images/logo.png')); ?>">

<!-- Bootstrap 5 CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

<!-- Remix Icons -->
<link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">

<!-- Bootstrap Icons -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">

<!-- Chart.js CSS (not needed for Chart.js v4) -->

<!-- Custom Grand-Style CSS -->
<link href="<?php echo e(asset('backend/css/grand-style.css')); ?>" rel="stylesheet" type="text/css" />

<?php echo $__env->yieldPushContent('styles'); ?>

<!-- Theme Variables -->
<script>
    // Theme configuration
    window.themeConfig = {
        layout: 'vertical',
        theme: 'light',
        sidebarSize: 'lg',
        sidebarView: 'default',
        sidebarColor: 'dark'
    };
</script>
<?php /**PATH C:\xampp\htdocs\Grandtek\resources\views/backend/components/head.blade.php ENDPATH**/ ?>