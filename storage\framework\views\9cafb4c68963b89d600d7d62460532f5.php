<?php $__env->startSection('title', 'Edit Slider - GrandTek Admin'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Edit Slider</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('sliders.index')); ?>">Sliders</a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<form action="<?php echo e(route('sliders.update', $slider)); ?>" method="POST" enctype="multipart/form-data" id="sliderForm">
    <?php echo csrf_field(); ?>
    <?php echo method_field('PUT'); ?>
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Slider Content</h5>
                </div>
                <div class="card-body">
                    <!-- Title -->
                    <div class="mb-3">
                        <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                        <input type="text" class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               id="title" name="title" value="<?php echo e(old('title', $slider->title)); ?>" 
                               placeholder="Enter slider title" required>
                        <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Subtitle -->
                    <div class="mb-3">
                        <label for="subtitle" class="form-label">Subtitle</label>
                        <input type="text" class="form-control <?php $__errorArgs = ['subtitle'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               id="subtitle" name="subtitle" value="<?php echo e(old('subtitle', $slider->subtitle)); ?>" 
                               placeholder="Enter slider subtitle">
                        <?php $__errorArgs = ['subtitle'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Description -->
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                  id="description" name="description" rows="4" 
                                  placeholder="Enter slider description"><?php echo e(old('description', $slider->description)); ?></textarea>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Button Settings -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="button_text" class="form-label">Button Text</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['button_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="button_text" name="button_text" value="<?php echo e(old('button_text', $slider->button_text)); ?>" 
                                       placeholder="e.g., Learn More">
                                <?php $__errorArgs = ['button_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="button_link" class="form-label">Button Link</label>
                                <input type="url" class="form-control <?php $__errorArgs = ['button_link'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="button_link" name="button_link" value="<?php echo e(old('button_link', $slider->button_link)); ?>" 
                                       placeholder="https://example.com">
                                <?php $__errorArgs = ['button_link'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Media Upload -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Media Upload</h5>
                </div>
                <div class="card-body">
                    <!-- Current Media Preview -->
                    <?php if($slider->hasMedia()): ?>
                        <div class="mb-3">
                            <label class="form-label">Current Media</label>
                            <div class="current-media-preview">
                                <?php if($slider->media_type === 'video' && $slider->video_path): ?>
                                    <video controls style="max-height: 200px; max-width: 100%;">
                                        <source src="<?php echo e(Storage::url($slider->video_path)); ?>" type="video/mp4">
                                    </video>
                                <?php elseif($slider->image_path): ?>
                                    <img src="<?php echo e(Storage::url($slider->image_path)); ?>" class="img-thumbnail" 
                                         style="max-height: 200px;" alt="<?php echo e($slider->title); ?>">
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Media Type Selection -->
                    <div class="mb-3">
                        <label class="form-label">Media Type <span class="text-danger">*</span></label>
                        <div class="d-flex gap-3">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="media_type" 
                                       id="media_image" value="image" 
                                       <?php echo e(old('media_type', $slider->media_type) === 'image' ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="media_image">
                                    <i class="ri-image-line me-1"></i>Image
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="media_type" 
                                       id="media_video" value="video" 
                                       <?php echo e(old('media_type', $slider->media_type) === 'video' ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="media_video">
                                    <i class="ri-video-line me-1"></i>Video
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Image Upload -->
                    <div class="mb-3" id="imageUpload">
                        <label for="image" class="form-label">Slider Image</label>
                        <input type="file" class="form-control <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               id="image" name="image" accept="image/*">
                        <div class="form-text">Leave empty to keep current image. Recommended size: 1920x1080px. Max size: 5MB</div>
                        <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <div id="imagePreview" class="mt-2"></div>
                    </div>

                    <!-- Video Upload -->
                    <div class="mb-3" id="videoUpload" style="display: none;">
                        <label for="video" class="form-label">Slider Video</label>
                        <input type="file" class="form-control <?php $__errorArgs = ['video'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               id="video" name="video" accept="video/*">
                        <div class="form-text">Leave empty to keep current video. Supported formats: MP4, WebM, OGG. Max size: 50MB</div>
                        <?php $__errorArgs = ['video'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <div id="videoPreview" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar Settings -->
        <div class="col-lg-4">
            <!-- Status & Visibility -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Status & Visibility</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="status" name="status">
                            <option value="active" <?php echo e(old('status', $slider->status) === 'active' ? 'selected' : ''); ?>>Active</option>
                            <option value="inactive" <?php echo e(old('status', $slider->status) === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                        </select>
                        <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
            </div>

            <!-- Design Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Design Settings</h5>
                </div>
                <div class="card-body">
                    <!-- Text Alignment -->
                    <div class="mb-3">
                        <label for="text_alignment" class="form-label">Text Alignment</label>
                        <select class="form-select <?php $__errorArgs = ['text_alignment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                id="text_alignment" name="text_alignment">
                            <option value="left" <?php echo e(old('text_alignment', $slider->text_alignment) === 'left' ? 'selected' : ''); ?>>Left</option>
                            <option value="center" <?php echo e(old('text_alignment', $slider->text_alignment) === 'center' ? 'selected' : ''); ?>>Center</option>
                            <option value="right" <?php echo e(old('text_alignment', $slider->text_alignment) === 'right' ? 'selected' : ''); ?>>Right</option>
                        </select>
                        <?php $__errorArgs = ['text_alignment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Display Duration -->
                    <div class="mb-3">
                        <label for="display_duration" class="form-label">Display Duration <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" class="form-control <?php $__errorArgs = ['display_duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="display_duration" name="display_duration"
                                   value="<?php echo e(old('display_duration', $slider->display_duration ?? 12)); ?>"
                                   min="3" max="60" step="1" required>
                            <span class="input-group-text">seconds</span>
                        </div>
                        <div class="form-text">
                            <i class="ri-information-line me-1"></i>
                            How long this slide will be displayed (3-60 seconds). Recommended: 8-15 seconds.
                        </div>
                        <?php $__errorArgs = ['display_duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Text Color -->
                    <div class="mb-3">
                        <label for="text_color" class="form-label">Text Color</label>
                        <input type="color" class="form-control form-control-color <?php $__errorArgs = ['text_color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               id="text_color" name="text_color" value="<?php echo e(old('text_color', $slider->text_color)); ?>">
                        <?php $__errorArgs = ['text_color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Background Color -->
                    <div class="mb-3">
                        <label for="background_color" class="form-label">Background Overlay Color</label>
                        <input type="color" class="form-control form-control-color <?php $__errorArgs = ['background_color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               id="background_color" name="background_color" value="<?php echo e(old('background_color', $slider->background_color)); ?>">
                        <div class="form-text">Optional overlay color for better text readability</div>
                        <?php $__errorArgs = ['background_color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
            </div>

            <!-- Animation Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Animation Settings</h5>
                </div>
                <div class="card-body">
                    <?php
                        $animations = $slider->animation_settings;
                    ?>
                    <div class="row">
                        <div class="col-6">
                            <div class="mb-3">
                                <label for="title_animation" class="form-label">Title Animation</label>
                                <select class="form-select" id="title_animation" name="title_animation">
                                    <option value="fadeInUp" <?php echo e(($animations['title_animation'] ?? 'fadeInUp') === 'fadeInUp' ? 'selected' : ''); ?>>Fade In Up</option>
                                    <option value="fadeInDown" <?php echo e(($animations['title_animation'] ?? '') === 'fadeInDown' ? 'selected' : ''); ?>>Fade In Down</option>
                                    <option value="fadeInLeft" <?php echo e(($animations['title_animation'] ?? '') === 'fadeInLeft' ? 'selected' : ''); ?>>Fade In Left</option>
                                    <option value="fadeInRight" <?php echo e(($animations['title_animation'] ?? '') === 'fadeInRight' ? 'selected' : ''); ?>>Fade In Right</option>
                                    <option value="zoomIn" <?php echo e(($animations['title_animation'] ?? '') === 'zoomIn' ? 'selected' : ''); ?>>Zoom In</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                <label for="subtitle_animation" class="form-label">Subtitle Animation</label>
                                <select class="form-select" id="subtitle_animation" name="subtitle_animation">
                                    <option value="fadeInLeft" <?php echo e(($animations['subtitle_animation'] ?? 'fadeInLeft') === 'fadeInLeft' ? 'selected' : ''); ?>>Fade In Left</option>
                                    <option value="fadeInRight" <?php echo e(($animations['subtitle_animation'] ?? '') === 'fadeInRight' ? 'selected' : ''); ?>>Fade In Right</option>
                                    <option value="fadeInUp" <?php echo e(($animations['subtitle_animation'] ?? '') === 'fadeInUp' ? 'selected' : ''); ?>>Fade In Up</option>
                                    <option value="fadeInDown" <?php echo e(($animations['subtitle_animation'] ?? '') === 'fadeInDown' ? 'selected' : ''); ?>>Fade In Down</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="mb-3">
                                <label for="animation_duration" class="form-label">Duration (ms)</label>
                                <input type="number" class="form-control" id="animation_duration" 
                                       name="animation_duration" value="<?php echo e($animations['duration'] ?? 1000); ?>" min="100" max="5000">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                <label for="animation_delay" class="form-label">Delay (ms)</label>
                                <input type="number" class="form-control" id="animation_delay" 
                                       name="animation_delay" value="<?php echo e($animations['delay'] ?? 200); ?>" min="0" max="2000">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i>Update Slider
                        </button>
                        <a href="<?php echo e(route('sliders.show', $slider)); ?>" class="btn btn-outline-info">
                            <i class="ri-eye-line me-1"></i>Preview Slider
                        </a>
                        <a href="<?php echo e(route('sliders.index')); ?>" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Sliders
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Media type toggle
document.querySelectorAll('input[name="media_type"]').forEach(radio => {
    radio.addEventListener('change', function() {
        toggleMediaUpload();
    });
});

function toggleMediaUpload() {
    const mediaType = document.querySelector('input[name="media_type"]:checked').value;
    const imageUpload = document.getElementById('imageUpload');
    const videoUpload = document.getElementById('videoUpload');
    
    if (mediaType === 'image') {
        imageUpload.style.display = 'block';
        videoUpload.style.display = 'none';
    } else {
        imageUpload.style.display = 'none';
        videoUpload.style.display = 'block';
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleMediaUpload();
});

// Image preview
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('imagePreview');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" class="img-thumbnail" style="max-height: 200px;">`;
        };
        reader.readAsDataURL(file);
    } else {
        preview.innerHTML = '';
    }
});

// Video preview
document.getElementById('video').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('videoPreview');
    
    if (file) {
        const url = URL.createObjectURL(file);
        preview.innerHTML = `<video controls style="max-height: 200px; max-width: 100%;">
                                <source src="${url}" type="${file.type}">
                            </video>`;
    } else {
        preview.innerHTML = '';
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('backend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\Grandtek\resources\views/backend/pages/sliders/edit.blade.php ENDPATH**/ ?>