<?php

namespace Tests\Feature;

use App\Models\BlogPost;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class BlogTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a user for authentication
        $this->user = User::factory()->create();
    }

    /** @test */
    public function it_can_create_blog_post_with_tags_as_string()
    {
        $this->actingAs($this->user);

        $postData = [
            'title' => 'Test Blog Post',
            'excerpt' => 'This is a test excerpt',
            'content' => 'This is the test content for the blog post.',
            'status' => 'published',
            'tags' => 'php, laravel, testing',
            'categories' => 'technology, web development',
        ];

        $response = $this->post(route('admin.blog.store'), $postData);

        $response->assertRedirect(route('admin.blog.index'));
        
        $this->assertDatabaseHas('blog_posts', [
            'title' => 'Test Blog Post',
            'excerpt' => 'This is a test excerpt',
        ]);

        $blogPost = BlogPost::where('title', 'Test Blog Post')->first();
        
        // Verify tags are stored as array
        $this->assertIsArray($blogPost->tags);
        $this->assertContains('php', $blogPost->tags);
        $this->assertContains('laravel', $blogPost->tags);
        $this->assertContains('testing', $blogPost->tags);
        
        // Verify categories are stored as array
        $this->assertIsArray($blogPost->categories);
        $this->assertContains('technology', $blogPost->categories);
        $this->assertContains('web development', $blogPost->categories);
    }

    /** @test */
    public function it_can_update_blog_post_with_tags_as_string()
    {
        $this->actingAs($this->user);

        // Create a blog post first
        $blogPost = BlogPost::create([
            'title' => 'Original Title',
            'excerpt' => 'Original excerpt',
            'content' => 'Original content',
            'status' => 'draft',
            'tags' => ['old-tag'],
            'categories' => ['old-category'],
        ]);

        $updateData = [
            'title' => 'Updated Title',
            'excerpt' => 'Updated excerpt',
            'content' => 'Updated content',
            'status' => 'published',
            'tags' => 'new-tag, updated-tag, php',
            'categories' => 'new-category, technology',
        ];

        $response = $this->put(route('admin.blog.update', $blogPost->id), $updateData);

        $response->assertRedirect(route('admin.blog.index'));
        
        $blogPost->refresh();
        
        // Verify tags are updated and stored as array
        $this->assertIsArray($blogPost->tags);
        $this->assertContains('new-tag', $blogPost->tags);
        $this->assertContains('updated-tag', $blogPost->tags);
        $this->assertContains('php', $blogPost->tags);
        $this->assertNotContains('old-tag', $blogPost->tags);
        
        // Verify categories are updated and stored as array
        $this->assertIsArray($blogPost->categories);
        $this->assertContains('new-category', $blogPost->categories);
        $this->assertContains('technology', $blogPost->categories);
        $this->assertNotContains('old-category', $blogPost->categories);
    }

    /** @test */
    public function it_handles_empty_tags_and_categories()
    {
        $this->actingAs($this->user);

        $postData = [
            'title' => 'Test Blog Post',
            'excerpt' => 'This is a test excerpt',
            'content' => 'This is the test content for the blog post.',
            'status' => 'published',
            'tags' => '',
            'categories' => '',
        ];

        $response = $this->post(route('admin.blog.store'), $postData);

        $response->assertRedirect(route('admin.blog.index'));
        
        $blogPost = BlogPost::where('title', 'Test Blog Post')->first();
        
        // Verify empty tags and categories are stored as empty arrays
        $this->assertIsArray($blogPost->tags);
        $this->assertEmpty($blogPost->tags);
        
        $this->assertIsArray($blogPost->categories);
        $this->assertEmpty($blogPost->categories);
    }
}
