<?php

namespace App\Http\Controllers;

use App\Models\System;
use App\Models\SystemRating;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class SystemController extends Controller
{
    /**
     * Display a listing of systems for admin
     */
    public function index()
    {
        try {
            $systems = System::with(['ratings' => function($query) {
                $query->active()->latest()->limit(3);
            }])->ordered()->get();
            
            return view('backend.pages.systems.index', compact('systems'));
        } catch (\Exception $e) {
            Log::error('Error loading systems: ' . $e->getMessage());
            return view('backend.pages.systems.index', ['systems' => collect()]);
        }
    }

    /**
     * Show the form for creating a new system
     */
    public function create()
    {
        return view('backend.pages.systems.create');
    }

    /**
     * Store a newly created system
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:systems,slug',
            'description' => 'required|string|max:1000',
            'short_description' => 'nullable|string|max:500',
            'full_description' => 'nullable|string',
            'category' => 'nullable|string|max:100',
            'technologies' => 'nullable|array',
            'technologies.*' => 'string|max:50',
            'system_url' => 'nullable|url|max:255',
            'documentation_url' => 'nullable|url|max:255',
            'system_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'features' => 'nullable|array',
            'features.*' => 'string|max:255',
            'status' => 'required|in:active,inactive,development,maintenance',
            'featured' => 'boolean',
            'launch_date' => 'nullable|date',
            'version' => 'nullable|string|max:20',
            'changelog' => 'nullable|string',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->only([
            'title', 'slug', 'description', 'short_description', 'full_description',
            'category', 'technologies', 'system_url', 'documentation_url',
            'features', 'status', 'launch_date', 'version', 'changelog',
            'meta_title', 'meta_description', 'meta_keywords'
        ]);

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['title']);
        }

        // Handle main system image upload
        if ($request->hasFile('system_image')) {
            $data['system_image'] = $request->file('system_image')->store('systems/images', 'public');
        }

        // Handle gallery images upload
        if ($request->hasFile('gallery_images')) {
            $galleryImages = [];
            foreach ($request->file('gallery_images') as $image) {
                $galleryImages[] = $image->store('systems/gallery', 'public');
            }
            $data['gallery_images'] = $galleryImages;
        }

        // Set featured flag
        $data['featured'] = $request->has('featured');

        try {
            Log::info('Creating system with data', ['data' => $data]);
            $system = System::create($data);
            Log::info('System created successfully', ['system_id' => $system->id]);

            return redirect()->route('systems.index')
                ->with('success', 'System created successfully!');
        } catch (\Exception $e) {
            Log::error('Error creating system', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            return redirect()->back()
                ->withErrors(['error' => 'Failed to create system: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Display the specified system for admin
     */
    public function show(System $system)
    {
        $system->load(['ratings' => function($query) {
            $query->active()->latest();
        }]);
        
        $ratingDistribution = $system->getRatingDistribution();
        
        return view('backend.pages.systems.show', compact('system', 'ratingDistribution'));
    }

    /**
     * Show the form for editing the specified system
     */
    public function edit(System $system)
    {
        return view('backend.pages.systems.edit', compact('system'));
    }

    /**
     * Update the specified system
     */
    public function update(Request $request, System $system)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:systems,slug,' . $system->id,
            'description' => 'required|string|max:1000',
            'short_description' => 'nullable|string|max:500',
            'full_description' => 'nullable|string',
            'category' => 'nullable|string|max:100',
            'technologies' => 'nullable|array',
            'technologies.*' => 'string|max:50',
            'system_url' => 'nullable|url|max:255',
            'documentation_url' => 'nullable|url|max:255',
            'system_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'features' => 'nullable|array',
            'features.*' => 'string|max:255',
            'status' => 'required|in:active,inactive,development,maintenance',
            'featured' => 'boolean',
            'launch_date' => 'nullable|date',
            'version' => 'nullable|string|max:20',
            'changelog' => 'nullable|string',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->only([
            'title', 'slug', 'description', 'short_description', 'full_description',
            'category', 'technologies', 'system_url', 'documentation_url',
            'features', 'status', 'launch_date', 'version', 'changelog',
            'meta_title', 'meta_description', 'meta_keywords'
        ]);

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['title']);
        }

        // Handle main system image upload
        if ($request->hasFile('system_image')) {
            // Delete old image
            if ($system->system_image && Storage::disk('public')->exists($system->system_image)) {
                Storage::disk('public')->delete($system->system_image);
            }
            $data['system_image'] = $request->file('system_image')->store('systems/images', 'public');
        }

        // Handle gallery images upload
        if ($request->hasFile('gallery_images')) {
            // Delete old gallery images if replacing
            if ($system->gallery_images) {
                foreach ($system->gallery_images as $image) {
                    if (Storage::disk('public')->exists($image)) {
                        Storage::disk('public')->delete($image);
                    }
                }
            }
            
            $galleryImages = [];
            foreach ($request->file('gallery_images') as $image) {
                $galleryImages[] = $image->store('systems/gallery', 'public');
            }
            $data['gallery_images'] = $galleryImages;
        }

        // Set featured flag
        $data['featured'] = $request->has('featured');

        try {
            $system->update($data);
            return redirect()->route('systems.index')
                ->with('success', 'System updated successfully!');
        } catch (\Exception $e) {
            Log::error('Error updating system', [
                'error' => $e->getMessage(),
                'system_id' => $system->id,
                'data' => $data
            ]);
            return redirect()->back()
                ->withErrors(['error' => 'Failed to update system: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Remove the specified system
     */
    public function destroy(System $system)
    {
        try {
            $system->delete();
            return redirect()->route('systems.index')
                ->with('success', 'System deleted successfully!');
        } catch (\Exception $e) {
            Log::error('Error deleting system', [
                'error' => $e->getMessage(),
                'system_id' => $system->id
            ]);
            return redirect()->back()
                ->withErrors(['error' => 'Failed to delete system: ' . $e->getMessage()]);
        }
    }

    /**
     * Get systems data for homepage
     */
    public function getHomepageData()
    {
        $systems = System::active()->featured()->ordered()->limit(6)->get();

        if ($systems->isEmpty()) {
            return null;
        }

        return $systems->map(function ($system) {
            return [
                'id' => $system->id,
                'title' => $system->title,
                'slug' => $system->slug,
                'description' => $system->description,
                'short_description' => $system->short_description,
                'category' => $system->category,
                'technologies' => $system->technologies,
                'system_url' => $system->system_url,
                'system_image_url' => $system->system_image_url,
                'average_rating' => $system->average_rating,
                'total_ratings' => $system->total_ratings,
                'launch_date' => $system->launch_date,
                'version' => $system->version,
                'features' => $system->features,
                'status' => $system->status,
            ];
        });
    }

    /**
     * Display system detail page for frontend
     */
    public function showFrontend(System $system)
    {
        // Increment views count
        $system->incrementViews();

        // Load ratings with pagination
        $ratings = $system->activeRatings()->latest()->paginate(10);
        $ratingDistribution = $system->getRatingDistribution();
        $featuredRatings = $system->featuredRatings()->latest()->limit(3)->get();

        return view('frontend.systems.show', compact('system', 'ratings', 'ratingDistribution', 'featuredRatings'));
    }

    /**
     * Store a rating for a system
     */
    public function storeRating(Request $request, System $system)
    {
        $validator = Validator::make($request->all(), [
            'rating' => 'required|integer|min:1|max:5',
            'review' => 'nullable|string|max:1000',
            'user_name' => 'required_without:user_id|string|max:255',
            'user_email' => 'required_without:user_id|email|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $data = [
            'system_id' => $system->id,
            'rating' => $request->rating,
            'review' => $request->review,
            'ip_address' => $request->ip(),
            'status' => 'active',
        ];

        // Check if user is authenticated
        if (Auth::check()) {
            $data['user_id'] = Auth::id();
            $data['is_verified'] = true;

            // Check if user already rated this system
            $existingRating = SystemRating::where('system_id', $system->id)
                ->where('user_id', Auth::id())
                ->first();

            if ($existingRating) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already rated this system.'
                ], 422);
            }
        } else {
            $data['user_name'] = $request->user_name;
            $data['user_email'] = $request->user_email;
            $data['is_verified'] = false;
        }

        try {
            $rating = SystemRating::create($data);

            return response()->json([
                'success' => true,
                'message' => 'Rating submitted successfully!',
                'rating' => [
                    'id' => $rating->id,
                    'rating' => $rating->rating,
                    'review' => $rating->review,
                    'display_name' => $rating->display_name,
                    'created_at' => $rating->created_at->format('M d, Y'),
                ],
                'system' => [
                    'average_rating' => $system->fresh()->average_rating,
                    'total_ratings' => $system->fresh()->total_ratings,
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating rating', [
                'error' => $e->getMessage(),
                'system_id' => $system->id,
                'data' => $data
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to submit rating. Please try again.'
            ], 500);
        }
    }

    /**
     * Update sort order for systems (AJAX)
     */
    public function updateSortOrder(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'systems' => 'required|array',
            'systems.*.id' => 'required|exists:systems,id',
            'systems.*.sort_order' => 'required|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            foreach ($request->systems as $systemData) {
                System::where('id', $systemData['id'])
                    ->update(['sort_order' => $systemData['sort_order']]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Sort order updated successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating sort order', [
                'error' => $e->getMessage(),
                'systems' => $request->systems
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update sort order.'
            ], 500);
        }
    }

    /**
     * Toggle system status
     */
    public function toggleStatus(System $system)
    {
        try {
            $newStatus = $system->status === 'active' ? 'inactive' : 'active';
            $system->update(['status' => $newStatus]);

            return response()->json([
                'success' => true,
                'status' => $newStatus,
                'message' => 'System status updated successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error('Error toggling system status: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update system status'
            ], 500);
        }
    }

    /**
     * Toggle featured status
     */
    public function toggleFeatured(System $system)
    {
        try {
            $system->update(['featured' => !$system->featured]);

            return response()->json([
                'success' => true,
                'featured' => $system->featured,
                'message' => $system->featured ? 'System marked as featured!' : 'System removed from featured!'
            ]);
        } catch (\Exception $e) {
            Log::error('Error toggling featured status', [
                'error' => $e->getMessage(),
                'system_id' => $system->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update featured status.'
            ], 500);
        }
    }

    /**
     * Reorder systems
     */
    public function reorder(Request $request)
    {
        try {
            $systems = $request->input('systems', []);

            foreach ($systems as $system) {
                System::where('id', $system['id'])->update(['sort_order' => $system['sort_order']]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Systems reordered successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error('Error reordering systems: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to reorder systems'
            ], 500);
        }
    }

    /**
     * Duplicate a system
     */
    public function duplicate(System $system)
    {
        try {
            $newSystem = $system->replicate();
            $newSystem->title = $system->title . ' (Copy)';
            $newSystem->slug = null; // Will be auto-generated
            $newSystem->status = 'inactive'; // Set copy as inactive by default
            $newSystem->sort_order = System::max('sort_order') + 1;
            $newSystem->save();

            return redirect()->route('systems.index')
                ->with('success', 'System duplicated successfully!');
        } catch (\Exception $e) {
            Log::error('Error duplicating system: ' . $e->getMessage());
            return redirect()->route('systems.index')
                ->with('error', 'Failed to duplicate system');
        }
    }

    /**
     * Remove gallery image
     */
    public function removeGalleryImage(System $system, $image_index)
    {
        try {
            $galleryImages = $system->gallery_images ?? [];

            if (isset($galleryImages[$image_index])) {
                $imagePath = $galleryImages[$image_index];

                // Delete file from storage
                if (Storage::disk('public')->exists($imagePath)) {
                    Storage::disk('public')->delete($imagePath);
                }

                // Remove from array
                unset($galleryImages[$image_index]);
                $galleryImages = array_values($galleryImages); // Re-index array

                $system->update(['gallery_images' => $galleryImages]);

                return response()->json([
                    'success' => true,
                    'message' => 'Gallery image removed successfully!'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Image not found'
            ], 404);
        } catch (\Exception $e) {
            Log::error('Error removing gallery image: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove image'
            ], 500);
        }
    }

    /**
     * Display all systems for frontend
     */
    public function allSystems()
    {
        $systems = System::active()->with(['ratings' => function($query) {
            $query->active()->latest()->limit(3);
        }])->ordered()->paginate(12);

        return view('frontend.systems.index', compact('systems'));
    }
}
