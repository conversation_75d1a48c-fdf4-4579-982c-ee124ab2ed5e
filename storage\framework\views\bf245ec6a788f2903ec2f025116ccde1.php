<?php $__env->startSection('title', 'Dashboard - GrandTek Admin'); ?>

<?php $__env->startPush('styles'); ?>
<style>
.quick-action-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;
}
.quick-action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}
.status-active { background-color: #28a745; }
.status-inactive { background-color: #dc3545; }
.status-draft { background-color: #ffc107; }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Dashboard</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="javascript: void(0);">GrandTek</a></li>
                    <li class="breadcrumb-item active">Dashboard</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard content will go here -->
<div class="row">
    <div class="col-xl-3 col-md-6">
        <div class="card card-animate">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1 overflow-hidden">
                        <p class="text-uppercase fw-medium text-muted text-truncate mb-0">Total Services</p>
                    </div>
                    <div class="flex-shrink-0">
                        <h5 class="text-success fs-14 mb-0">
                            <i class="ri-arrow-right-up-line fs-13 align-middle"></i> +16.24%
                        </h5>
                    </div>
                </div>
                <div class="d-flex align-items-end justify-content-between mt-4">
                    <div>
                        <h4 class="fs-22 fw-semibold ff-secondary mb-4"><span class="counter-value" data-target="559.25">0</span>k</h4>
                        <a href="<?php echo e(route('all.service')); ?>" class="text-decoration-underline">View Services</a>
                    </div>
                    <div class="avatar-sm flex-shrink-0">
                        <span class="avatar-title bg-success-subtle rounded fs-3">
                            <i class="ri-customer-service-2-line text-success"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card card-animate">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1 overflow-hidden">
                        <p class="text-uppercase fw-medium text-muted text-truncate mb-0">Total Projects</p>
                    </div>
                    <div class="flex-shrink-0">
                        <h5 class="text-danger fs-14 mb-0">
                            <i class="ri-arrow-right-down-line fs-13 align-middle"></i> -3.57%
                        </h5>
                    </div>
                </div>
                <div class="d-flex align-items-end justify-content-between mt-4">
                    <div>
                        <h4 class="fs-22 fw-semibold ff-secondary mb-4"><span class="counter-value" data-target="36894">0</span></h4>
                        <a href="<?php echo e(route('projects.index')); ?>" class="text-decoration-underline">View Projects</a>
                    </div>
                    <div class="avatar-sm flex-shrink-0">
                        <span class="avatar-title bg-info-subtle rounded fs-3">
                            <i class="ri-folder-3-line text-info"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card card-animate">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1 overflow-hidden">
                        <p class="text-uppercase fw-medium text-muted text-truncate mb-0">Team Members</p>
                    </div>
                    <div class="flex-shrink-0">
                        <h5 class="text-success fs-14 mb-0">
                            <i class="ri-arrow-right-up-line fs-13 align-middle"></i> +29.08%
                        </h5>
                    </div>
                </div>
                <div class="d-flex align-items-end justify-content-between mt-4">
                    <div>
                        <h4 class="fs-22 fw-semibold ff-secondary mb-4"><span class="counter-value" data-target="183.35">0</span>M</h4>
                        <a href="<?php echo e(route('all.teams')); ?>" class="text-decoration-underline">View Team</a>
                    </div>
                    <div class="avatar-sm flex-shrink-0">
                        <span class="avatar-title bg-warning-subtle rounded fs-3">
                            <i class="ri-team-line text-warning"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card card-animate">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1 overflow-hidden">
                        <p class="text-uppercase fw-medium text-muted text-truncate mb-0">Messages</p>
                    </div>
                    <div class="flex-shrink-0">
                        <h5 class="text-muted fs-14 mb-0">
                            +0.00%
                        </h5>
                    </div>
                </div>
                <div class="d-flex align-items-end justify-content-between mt-4">
                    <div>
                        <h4 class="fs-22 fw-semibold ff-secondary mb-4"><span class="counter-value" data-target="165.89">0</span>k</h4>
                        <a href="<?php echo e(route('messages')); ?>" class="text-decoration-underline">View Messages</a>
                    </div>
                    <div class="avatar-sm flex-shrink-0">
                        <span class="avatar-title bg-primary-subtle rounded fs-3">
                            <i class="ri-message-3-line text-primary"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Management Section -->
<div class="row mt-4">
    <div class="col-12">
        <h5 class="mb-3">Content Management</h5>
    </div>

    <div class="col-xl-4 col-md-6">
        <div class="card card-animate">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1 overflow-hidden">
                        <p class="text-uppercase fw-medium text-muted text-truncate mb-0">Homepage Sliders</p>
                    </div>
                    <div class="flex-shrink-0">
                        <span class="badge bg-success">Active</span>
                    </div>
                </div>
                <div class="d-flex align-items-end justify-content-between mt-4">
                    <div>
                        <h4 class="fs-22 fw-semibold ff-secondary mb-4">
                            <i class="ri-slideshow-line text-primary"></i>
                        </h4>
                        <a href="<?php echo e(route('sliders.index')); ?>" class="text-decoration-underline">Manage Sliders</a>
                    </div>
                    <div class="avatar-sm flex-shrink-0">
                        <span class="avatar-title bg-primary-subtle rounded fs-3">
                            <i class="ri-slideshow-line text-primary"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-6">
        <div class="card card-animate quick-action-card" onclick="window.location.href='<?php echo e(route('admin.about.index')); ?>'">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1 overflow-hidden">
                        <p class="text-uppercase fw-medium text-muted text-truncate mb-0">About Section</p>
                    </div>
                    <div class="flex-shrink-0">
                        <?php
                            $aboutExists = \App\Models\aboutus::first();
                        ?>
                        <?php if($aboutExists): ?>
                            <span class="status-indicator status-<?php echo e($aboutExists->status); ?>"></span>
                            <span class="badge bg-<?php echo e($aboutExists->status === 'active' ? 'success' : 'secondary'); ?>">
                                <?php echo e(ucfirst($aboutExists->status)); ?>

                            </span>
                        <?php else: ?>
                            <span class="badge bg-warning">Not Set</span>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="d-flex align-items-end justify-content-between mt-4">
                    <div>
                        <h4 class="fs-22 fw-semibold ff-secondary mb-4">
                            <i class="ri-information-line text-info"></i>
                        </h4>
                        <div class="d-flex gap-2">
                            <a href="<?php echo e(route('admin.about.index')); ?>" class="text-decoration-underline">Manage About</a>
                            <?php if($aboutExists): ?>
                                <span class="text-muted">|</span>
                                <a href="/about" target="_blank" class="text-decoration-underline text-primary">Preview</a>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="avatar-sm flex-shrink-0">
                        <span class="avatar-title bg-info-subtle rounded fs-3">
                            <i class="ri-information-line text-info"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-6">
        <div class="card card-animate">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1 overflow-hidden">
                        <p class="text-uppercase fw-medium text-muted text-truncate mb-0">Site Settings</p>
                    </div>
                    <div class="flex-shrink-0">
                        <span class="badge bg-warning">Configure</span>
                    </div>
                </div>
                <div class="d-flex align-items-end justify-content-between mt-4">
                    <div>
                        <h4 class="fs-22 fw-semibold ff-secondary mb-4">
                            <i class="ri-settings-4-line text-warning"></i>
                        </h4>
                        <a href="<?php echo e(route('site.setting')); ?>" class="text-decoration-underline">Site Settings</a>
                    </div>
                    <div class="avatar-sm flex-shrink-0">
                        <span class="avatar-title bg-warning-subtle rounded fs-3">
                            <i class="ri-settings-4-line text-warning"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- SEO Management Section -->
<div class="row mt-4">
    <div class="col-12">
        <h5 class="mb-3">SEO & Marketing</h5>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card card-animate quick-action-card" onclick="window.location.href='<?php echo e(route('admin.seo.dashboard')); ?>'">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1 overflow-hidden">
                        <p class="text-uppercase fw-medium text-muted text-truncate mb-0">SEO Dashboard</p>
                    </div>
                    <div class="flex-shrink-0">
                        <span class="badge bg-primary">Optimize</span>
                    </div>
                </div>
                <div class="d-flex align-items-end justify-content-between mt-4">
                    <div>
                        <h4 class="fs-22 fw-semibold ff-secondary mb-4">
                            <i class="ri-search-line text-primary"></i>
                        </h4>
                        <div class="d-flex gap-2">
                            <a href="<?php echo e(route('admin.seo.dashboard')); ?>" class="text-decoration-underline">SEO Dashboard</a>
                            <span class="text-muted">|</span>
                            <a href="<?php echo e(route('admin.seo.analysis')); ?>" class="text-decoration-underline text-info">Analysis</a>
                        </div>
                    </div>
                    <div class="avatar-sm flex-shrink-0">
                        <span class="avatar-title bg-primary-subtle rounded fs-3">
                            <i class="ri-search-line text-primary"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card card-animate quick-action-card" onclick="window.location.href='<?php echo e(route('admin.seo.pages')); ?>'">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1 overflow-hidden">
                        <p class="text-uppercase fw-medium text-muted text-truncate mb-0">Page SEO</p>
                    </div>
                    <div class="flex-shrink-0">
                        <?php
                            $totalPages = \App\Models\PageSeo::count();
                            $activePagesCount = \App\Models\PageSeo::where('is_active', true)->count();
                        ?>
                        <span class="badge bg-<?php echo e($activePagesCount > 0 ? 'success' : 'secondary'); ?>">
                            <?php echo e($totalPages); ?> Pages
                        </span>
                    </div>
                </div>
                <div class="d-flex align-items-end justify-content-between mt-4">
                    <div>
                        <h4 class="fs-22 fw-semibold ff-secondary mb-4">
                            <i class="ri-file-text-line text-success"></i>
                        </h4>
                        <div class="d-flex gap-2">
                            <a href="<?php echo e(route('admin.seo.pages')); ?>" class="text-decoration-underline">Manage Pages</a>
                            <span class="text-muted">|</span>
                            <a href="<?php echo e(route('admin.seo.pages.create')); ?>" class="text-decoration-underline text-primary">Add New</a>
                        </div>
                    </div>
                    <div class="avatar-sm flex-shrink-0">
                        <span class="avatar-title bg-success-subtle rounded fs-3">
                            <i class="ri-file-text-line text-success"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card card-animate quick-action-card" onclick="window.location.href='<?php echo e(route('admin.blog.index')); ?>'">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1 overflow-hidden">
                        <p class="text-uppercase fw-medium text-muted text-truncate mb-0">Blog Posts</p>
                    </div>
                    <div class="flex-shrink-0">
                        <?php
                            $totalPosts = \App\Models\BlogPost::count();
                            $publishedPosts = \App\Models\BlogPost::where('status', 'published')->count();
                        ?>
                        <span class="badge bg-<?php echo e($publishedPosts > 0 ? 'info' : 'secondary'); ?>">
                            <?php echo e($totalPosts); ?> Posts
                        </span>
                    </div>
                </div>
                <div class="d-flex align-items-end justify-content-between mt-4">
                    <div>
                        <h4 class="fs-22 fw-semibold ff-secondary mb-4">
                            <i class="ri-article-line text-info"></i>
                        </h4>
                        <div class="d-flex gap-2">
                            <a href="<?php echo e(route('admin.blog.index')); ?>" class="text-decoration-underline">All Posts</a>
                            <span class="text-muted">|</span>
                            <a href="<?php echo e(route('admin.blog.create')); ?>" class="text-decoration-underline text-primary">Create Post</a>
                        </div>
                    </div>
                    <div class="avatar-sm flex-shrink-0">
                        <span class="avatar-title bg-info-subtle rounded fs-3">
                            <i class="ri-article-line text-info"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card card-animate quick-action-card" onclick="window.location.href='<?php echo e(route('admin.seo.settings')); ?>'">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1 overflow-hidden">
                        <p class="text-uppercase fw-medium text-muted text-truncate mb-0">SEO Settings</p>
                    </div>
                    <div class="flex-shrink-0">
                        <?php
                            $seoSettingsCount = \App\Models\SeoSetting::where('is_active', true)->count();
                        ?>
                        <span class="badge bg-<?php echo e($seoSettingsCount > 0 ? 'warning' : 'secondary'); ?>">
                            <?php echo e($seoSettingsCount); ?> Active
                        </span>
                    </div>
                </div>
                <div class="d-flex align-items-end justify-content-between mt-4">
                    <div>
                        <h4 class="fs-22 fw-semibold ff-secondary mb-4">
                            <i class="ri-settings-3-line text-warning"></i>
                        </h4>
                        <div class="d-flex gap-2">
                            <a href="<?php echo e(route('admin.seo.settings')); ?>" class="text-decoration-underline">SEO Settings</a>
                            <span class="text-muted">|</span>
                            <a href="<?php echo e(route('sitemap')); ?>" target="_blank" class="text-decoration-underline text-primary">Sitemap</a>
                        </div>
                    </div>
                    <div class="avatar-sm flex-shrink-0">
                        <span class="avatar-title bg-warning-subtle rounded fs-3">
                            <i class="ri-settings-3-line text-warning"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\Grandtek\resources\views/backend/pages/dashboard.blade.php ENDPATH**/ ?>