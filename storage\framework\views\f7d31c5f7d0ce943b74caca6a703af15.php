<?php $__env->startSection('title', 'Login - GrandTek Admin'); ?>

<?php $__env->startSection('content'); ?>
<div class="auth-card">
    <!-- Logo -->
    <div class="auth-logo">
        <img src="<?php echo e(asset('backend/images/logo-icon.png')); ?>" alt="GrandTek Logo">
    </div>

    <!-- Header -->
    <div class="auth-card-header">
        <h1 class="auth-title">SIGN IN</h1>
    </div>

    <!-- Body -->
    <div class="auth-card-body">
        <!-- Session Status -->
        <?php if(session('status')): ?>
            <div class="alert alert-success">
                <?php echo e(session('status')); ?>

            </div>
        <?php endif; ?>

        <!-- Validation Errors -->
        <?php if($errors->any()): ?>
            <div class="alert alert-danger">
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div><?php echo e($error); ?></div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="<?php echo e(route('login')); ?>" id="loginForm">
            <?php echo csrf_field(); ?>

            <!-- Email Address -->
            <div class="form-group">
                <input type="email"
                       id="email"
                       name="email"
                       class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       placeholder="Email"
                       value="<?php echo e(old('email')); ?>"
                       required
                       autofocus
                       autocomplete="username">
                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback">
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Password -->
            <div class="form-group">
                <div class="input-group">
                    <input type="password"
                           id="password"
                           name="password"
                           class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           placeholder="Password"
                           required
                           autocomplete="current-password">
                    <button type="button" id="togglePassword">
                        <i class="ri-eye-line" id="toggleIcon"></i>
                    </button>
                </div>
                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback">
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Social Login -->
            <div class="social-login">
                <div class="social-icons">
                    <a href="#" class="social-icon">
                        <i class="ri-facebook-fill"></i>
                    </a>
                    <a href="#" class="social-icon">
                        <i class="ri-twitter-fill"></i>
                    </a>
                    <a href="#" class="social-icon">
                        <i class="ri-google-fill"></i>
                    </a>
                </div>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="btn-auth">
                <i class="ri-arrow-right-line"></i>
            </button>

            <!-- Remember Me & Forgot Password -->
            <?php if(Route::has('password.request')): ?>
                <a href="<?php echo e(route('password.request')); ?>" class="forgot-password">
                    Forget Your Password ?
                </a>
            <?php endif; ?>
        </form>
    </div>

    <!-- Footer -->
    <div class="auth-footer">
        <p class="mb-0">
            Don't have an account?
            <a href="<?php echo e(route('register')); ?>" class="auth-link">Sign Up here</a>
        </p>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.getElementById('toggleIcon');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'ri-eye-off-line';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'ri-eye-line';
        }
    });

    // Form validation
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        if (!email || !password) {
            e.preventDefault();
            alert('Please fill in all required fields.');
            return false;
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            e.preventDefault();
            alert('Please enter a valid email address.');
            return false;
        }
    });
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('auth.layouts.auth', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\Grandtek\resources\views/auth/login.blade.php ENDPATH**/ ?>