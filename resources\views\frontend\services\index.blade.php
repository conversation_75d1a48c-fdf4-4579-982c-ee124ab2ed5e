@extends('frontend.layouts.app')

@section('title', 'Our Services - Grandtek')

@section('content')
<!-- <PERSON> Header -->
<div class="container-fluid page-header py-5">
    <div class="container text-center py-5">
        <h1 class="display-2 text-white mb-4 animated slideInDown">Our Services</h1>
        <nav aria-label="breadcrumb animated slideInDown">
            <ol class="breadcrumb justify-content-center mb-0">
                <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                <li class="breadcrumb-item text-white" aria-current="page">Services</li>
            </ol>
        </nav>
    </div>
</div>

<!-- Services Section -->
<div class="container-fluid services py-5">
    <div class="container">
        <div class="text-center mx-auto pb-5 wow fadeIn" data-wow-delay=".3s" style="max-width: 600px;">
            <h5 class="text-primary">Our Services</h5>
            <h1>Services Built Specifically For Your Business</h1>
            <p class="mb-0">Discover our comprehensive range of technology solutions designed to help your business grow and succeed in the digital age.</p>
        </div>

        @if($services->count() > 0)
            <div class="row g-5 services-inner">
                @foreach($services as $index => $service)
                    <div class="col-md-6 col-lg-4 wow fadeIn" data-wow-delay="{{ 0.3 + (($index % 6) * 0.2) }}s">
                        <div class="services-item bg-light h-100">
                            <div class="p-4 text-center services-content h-100 d-flex flex-column">
                                <div class="services-content-icon">
                                    @if($service->service_image_url)
                                        <img src="{{ $service->service_image_url }}" alt="{{ $service->name }}" 
                                             class="mb-4" style="width: 80px; height: 80px; object-fit: contain;">
                                    @else
                                        <i class="{{ $service->icon_class ?? 'fas fa-cogs' }} fa-7x mb-4 text-primary"></i>
                                    @endif
                                    <h4 class="mb-3">{{ $service->name }}</h4>
                                    <p class="mb-4 flex-grow-1">
                                        {{ $service->short_description ?: Str::limit($service->description, 120) }}
                                    </p>
                                    <div class="mt-auto">
                                        <a href="{{ route('service.detail', $service->slug) }}" 
                                           class="btn btn-primary text-white px-5 py-3 rounded-pill">
                                            <i class="ri-eye-line me-2"></i>View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            @if($services->hasPages())
                <div class="d-flex justify-content-center mt-5">
                    {{ $services->links() }}
                </div>
            @endif
        @else
            <div class="row">
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="ri-service-line text-muted" style="font-size: 5rem;"></i>
                        <h3 class="mt-4 text-muted">No Services Available</h3>
                        <p class="text-muted">We're working on adding new services. Please check back soon!</p>
                        <a href="{{ route('home') }}" class="btn btn-primary mt-3">
                            <i class="ri-home-line me-2"></i>Back to Home
                        </a>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>

<!-- Call to Action Section -->
<div class="container-fluid bg-primary py-5">
    <div class="container">
        <div class="row g-5 align-items-center">
            <div class="col-lg-8 wow fadeIn" data-wow-delay=".3s">
                <h2 class="text-white mb-3">Need a Custom Solution?</h2>
                <p class="text-white mb-0">
                    Can't find exactly what you're looking for? We specialize in creating custom technology solutions 
                    tailored to your specific business needs. Contact us to discuss your requirements.
                </p>
            </div>
            <div class="col-lg-4 text-lg-end wow fadeIn" data-wow-delay=".5s">
                <a href="{{ route('home') }}#contact" class="btn btn-light btn-lg px-5 py-3 rounded-pill">
                    <i class="ri-phone-line me-2"></i>Contact Us
                </a>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.services-item {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    overflow: hidden;
}

.services-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-color: var(--bs-primary);
}

.services-content-icon i {
    transition: all 0.3s ease;
}

.services-item:hover .services-content-icon i {
    transform: scale(1.1);
    color: var(--bs-primary) !important;
}

.page-header {
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), 
                url('/frontend/img/carousel-1.jpg') center center/cover;
    min-height: 400px;
    display: flex;
    align-items: center;
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: white;
}

.btn-primary {
    background: linear-gradient(45deg, var(--bs-primary), #0056b3);
    border: none;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}
</style>
@endpush
