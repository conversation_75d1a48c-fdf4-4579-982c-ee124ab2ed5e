<?php $__env->startSection('title', 'Register - GrandTek Admin'); ?>

<?php $__env->startSection('content'); ?>
<div class="auth-card">
    <!-- Logo -->
    <div class="auth-logo">
        <img src="<?php echo e(asset('backend/images/logo-icon.png')); ?>" alt="GrandTek Logo">
    </div>

    <!-- Header -->
    <div class="auth-card-header">
        <h1 class="auth-title">SIGN UP</h1>
    </div>

    <!-- Body -->
    <div class="auth-card-body">
        <!-- Session Status -->
        <?php if(session('status')): ?>
            <div class="alert alert-success">
                <?php echo e(session('status')); ?>

            </div>
        <?php endif; ?>

        <!-- Validation Errors -->
        <?php if($errors->any()): ?>
            <div class="alert alert-danger">
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div><?php echo e($error); ?></div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="<?php echo e(route('register')); ?>" id="registerForm">
            <?php echo csrf_field(); ?>

            <!-- Full Name -->
            <div class="form-group">
                <input type="text"
                       id="name"
                       name="name"
                       class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       placeholder="Full Name"
                       value="<?php echo e(old('name')); ?>"
                       required
                       autofocus
                       autocomplete="name">
                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback">
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Email Address -->
            <div class="form-group">
                <input type="email"
                       id="email"
                       name="email"
                       class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       placeholder="Email"
                       value="<?php echo e(old('email')); ?>"
                       required
                       autocomplete="username">
                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback">
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Password -->
            <div class="form-group">
                <div class="input-group">
                    <input type="password"
                           id="password"
                           name="password"
                           class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           placeholder="Password"
                           required
                           autocomplete="new-password">
                    <button type="button" id="togglePassword">
                        <i class="ri-eye-line" id="toggleIcon"></i>
                    </button>
                </div>
                <div class="password-strength" id="passwordStrength" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small>Password strength: <span id="strengthText">Weak</span></small>
                </div>
                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback">
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Confirm Password -->
            <div class="form-group">
                <div class="input-group">
                    <input type="password"
                           id="password_confirmation"
                           name="password_confirmation"
                           class="form-control <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           placeholder="Confirm Password"
                           required
                           autocomplete="new-password">
                    <button type="button" id="togglePasswordConfirm">
                        <i class="ri-eye-line" id="toggleIconConfirm"></i>
                    </button>
                </div>
                <div id="passwordMatch" style="display: none;">
                    <small><i class="ri-check-line"></i> Passwords match</small>
                </div>
                <div id="passwordMismatch" style="display: none;">
                    <small><i class="ri-close-line"></i> Passwords do not match</small>
                </div>
                <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback">
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Social Login -->
            <div class="social-login">
                <div class="social-icons">
                    <a href="#" class="social-icon">
                        <i class="ri-facebook-fill"></i>
                    </a>
                    <a href="#" class="social-icon">
                        <i class="ri-twitter-fill"></i>
                    </a>
                    <a href="#" class="social-icon">
                        <i class="ri-google-fill"></i>
                    </a>
                </div>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="btn-auth">
                <i class="ri-arrow-right-line"></i>
            </button>

            <!-- Terms and Conditions -->
            <div class="form-check">
                <input type="checkbox"
                       class="form-check-input"
                       id="terms"
                       name="terms"
                       required>
                <label class="form-check-label" for="terms">
                    I agree to the <a href="#" class="auth-link">Terms of Service</a> and <a href="#" class="auth-link">Privacy Policy</a>
                </label>
            </div>
        </form>
    </div>

    <!-- Footer -->
    <div class="auth-footer">
        <p class="mb-0">
            Already have an account?
            <a href="<?php echo e(route('login')); ?>" class="auth-link">Sign In here</a>
        </p>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.getElementById('toggleIcon');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'ri-eye-off-line';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'ri-eye-line';
        }
    });

    // Toggle confirm password visibility
    document.getElementById('togglePasswordConfirm').addEventListener('click', function() {
        const passwordInput = document.getElementById('password_confirmation');
        const toggleIcon = document.getElementById('toggleIconConfirm');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'ri-eye-off-line';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'ri-eye-line';
        }
    });

    // Password strength checker
    document.getElementById('password').addEventListener('input', function() {
        const password = this.value;
        const strengthDiv = document.getElementById('passwordStrength');
        const progressBar = strengthDiv.querySelector('.progress-bar');
        const strengthText = document.getElementById('strengthText');

        if (password.length > 0) {
            strengthDiv.style.display = 'block';

            let strength = 0;
            let strengthLabel = 'Weak';
            let strengthColor = 'bg-danger';

            // Length check
            if (password.length >= 8) strength += 25;

            // Uppercase check
            if (/[A-Z]/.test(password)) strength += 25;

            // Lowercase check
            if (/[a-z]/.test(password)) strength += 25;

            // Number or special character check
            if (/[\d\W]/.test(password)) strength += 25;

            // Update strength label and color
            if (strength >= 75) {
                strengthLabel = 'Strong';
                strengthColor = 'bg-success';
            } else if (strength >= 50) {
                strengthLabel = 'Medium';
                strengthColor = 'bg-warning';
            }

            progressBar.style.width = strength + '%';
            progressBar.className = 'progress-bar ' + strengthColor;
            strengthText.textContent = strengthLabel;
        } else {
            strengthDiv.style.display = 'none';
        }
    });

    // Password confirmation checker
    function checkPasswordMatch() {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('password_confirmation').value;
        const matchDiv = document.getElementById('passwordMatch');
        const mismatchDiv = document.getElementById('passwordMismatch');

        if (confirmPassword.length > 0) {
            if (password === confirmPassword) {
                matchDiv.style.display = 'block';
                mismatchDiv.style.display = 'none';
            } else {
                matchDiv.style.display = 'none';
                mismatchDiv.style.display = 'block';
            }
        } else {
            matchDiv.style.display = 'none';
            mismatchDiv.style.display = 'none';
        }
    }

    document.getElementById('password').addEventListener('input', checkPasswordMatch);
    document.getElementById('password_confirmation').addEventListener('input', checkPasswordMatch);

    // Form validation
    document.getElementById('registerForm').addEventListener('submit', function(e) {
        const name = document.getElementById('name').value;
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const passwordConfirm = document.getElementById('password_confirmation').value;
        const terms = document.getElementById('terms').checked;

        if (!name || !email || !password || !passwordConfirm) {
            e.preventDefault();
            alert('Please fill in all required fields.');
            return false;
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            e.preventDefault();
            alert('Please enter a valid email address.');
            return false;
        }

        // Password match validation
        if (password !== passwordConfirm) {
            e.preventDefault();
            alert('Passwords do not match.');
            return false;
        }

        // Password strength validation
        if (password.length < 8) {
            e.preventDefault();
            alert('Password must be at least 8 characters long.');
            return false;
        }

        // Terms validation
        if (!terms) {
            e.preventDefault();
            alert('Please accept the Terms of Service and Privacy Policy.');
            return false;
        }
    });
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('auth.layouts.auth', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\Grandtek\resources\views/auth/register.blade.php ENDPATH**/ ?>