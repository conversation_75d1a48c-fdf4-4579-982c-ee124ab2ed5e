<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class SeoSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'label',
        'description',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get setting value by key
     */
    public static function get($key, $default = null)
    {
        return Cache::remember("seo_setting_{$key}", 3600, function () use ($key, $default) {
            $setting = static::where('key', $key)->where('is_active', true)->first();

            if (!$setting) {
                return $default;
            }

            return static::castValue($setting->value, $setting->type);
        });
    }

    /**
     * Set setting value
     */
    public static function set($key, $value, $type = 'text', $group = 'general', $label = null, $description = null)
    {
        $setting = static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
                'group' => $group,
                'label' => $label ?: $key,
                'description' => $description,
                'is_active' => true,
            ]
        );

        Cache::forget("seo_setting_{$key}");

        return $setting;
    }

    /**
     * Get all settings by group
     */
    public static function getByGroup($group)
    {
        return Cache::remember("seo_settings_group_{$group}", 3600, function () use ($group) {
            return static::where('group', $group)
                         ->where('is_active', true)
                         ->orderBy('sort_order')
                         ->get()
                         ->mapWithKeys(function ($setting) {
                             return [$setting->key => static::castValue($setting->value, $setting->type)];
                         });
        });
    }

    /**
     * Cast value based on type
     */
    protected static function castValue($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return (bool) $value;
            case 'integer':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'json':
                return json_decode($value, true);
            case 'array':
                return is_string($value) ? explode(',', $value) : (is_array($value) ? $value : []);
            default:
                return $value;
        }
    }

    /**
     * Clear all SEO settings cache
     */
    public static function clearCache()
    {
        $keys = static::pluck('key');
        foreach ($keys as $key) {
            Cache::forget("seo_setting_{$key}");
        }

        $groups = static::distinct('group')->pluck('group');
        foreach ($groups as $group) {
            Cache::forget("seo_settings_group_{$group}");
        }
    }

    /**
     * Boot method to clear cache on model events
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function ($model) {
            Cache::forget("seo_setting_{$model->key}");
            Cache::forget("seo_settings_group_{$model->group}");
        });

        static::deleted(function ($model) {
            Cache::forget("seo_setting_{$model->key}");
            Cache::forget("seo_settings_group_{$model->group}");
        });
    }

    /**
     * Scope for active settings
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific group
     */
    public function scopeGroup($query, $group)
    {
        return $query->where('group', $group);
    }

    /**
     * Get all available groups
     */
    public static function getGroups()
    {
        return [
            'general' => 'General Settings',
            'meta' => 'Meta Tags',
            'analytics' => 'Analytics & Tracking',
            'social' => 'Social Media',
            'local' => 'Local SEO',
            'technical' => 'Technical SEO',
        ];
    }

    /**
     * Get all available types
     */
    public static function getTypes()
    {
        return [
            'text' => 'Text',
            'textarea' => 'Textarea',
            'url' => 'URL',
            'email' => 'Email',
            'boolean' => 'Boolean',
            'integer' => 'Integer',
            'json' => 'JSON',
            'array' => 'Array (comma-separated)',
        ];
    }
}
