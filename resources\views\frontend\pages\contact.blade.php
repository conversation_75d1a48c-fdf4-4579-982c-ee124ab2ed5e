@extends('frontend.layouts.app')

@section('title', 'Contact Us - GrandTek IT Solutions')
@section('meta_description', 'Get in touch with GrandTek IT Solutions for professional software development services. Contact us for custom web applications, mobile apps, and IT consulting services.')
@section('keywords', 'contact GrandTek, software development contact, IT services contact, web development inquiry, mobile app development contact')
@section('page_type', 'contact')

@push('styles')
<style>
    .contact-hero {
        background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
        color: white;
        padding: 80px 0 60px;
    }
    
    .contact-form {
        background: var(--bs-light);
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .contact-info-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        transition: transform 0.3s ease;
    }
    
    .contact-info-card:hover {
        transform: translateY(-5px);
    }
    
    .contact-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--bs-primary), var(--bs-secondary));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
        margin-bottom: 20px;
    }
    
    .form-control:focus {
        border-color: var(--bs-primary);
        box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
    }
    
    .btn-contact {
        background: linear-gradient(135deg, var(--bs-primary), var(--bs-secondary));
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-contact:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
</style>
@endpush

@section('content')
<!-- Contact Hero Section -->
<section class="contact-hero">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-4">Get In Touch</h1>
                <p class="lead mb-4">Ready to transform your business with innovative technology solutions? We're here to help you succeed.</p>
                <div class="d-flex flex-wrap gap-3">
                    <a href="tel:+254700000000" class="btn btn-light btn-lg">
                        <i class="fas fa-phone me-2"></i>Call Now
                    </a>
                    <a href="mailto:<EMAIL>" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-envelope me-2"></i>Email Us
                    </a>
                </div>
            </div>
            <div class="col-lg-4 text-center">
                <i class="fas fa-comments display-1 opacity-75"></i>
            </div>
        </div>
    </div>
</section>

<!-- Contact Information Section -->
<section class="py-5">
    <div class="container">
        <!-- Contact Info Cards -->
        <div class="row mb-5">
            <div class="col-lg-4 mb-4">
                <div class="contact-info-card p-4 text-center h-100">
                    <div class="contact-icon mx-auto">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h4 class="h5 mb-3">Visit Our Office</h4>
                    <p class="text-muted mb-0">
                        Nairobi CBD<br>
                        Nairobi, Kenya
                    </p>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="contact-info-card p-4 text-center h-100">
                    <div class="contact-icon mx-auto">
                        <i class="fas fa-phone"></i>
                    </div>
                    <h4 class="h5 mb-3">Call Us</h4>
                    <p class="text-muted mb-0">
                        <a href="tel:+254700000000" class="text-decoration-none">+254 700 000 000</a><br>
                        <small>Mon - Fri: 8:00 AM - 6:00 PM</small>
                    </p>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="contact-info-card p-4 text-center h-100">
                    <div class="contact-icon mx-auto">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h4 class="h5 mb-3">Email Us</h4>
                    <p class="text-muted mb-0">
                        <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a><br>
                        <small>We'll respond within 24 hours</small>
                    </p>
                </div>
            </div>
        </div>

        <!-- Contact Form and Map -->
        <div class="row">
            <div class="col-lg-8">
                <div class="contact-form p-5">
                    <h2 class="h3 mb-4">Send Us a Message</h2>
                    
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif
                    
                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif
                    
                    <form action="{{ route('contact.submit') }}" method="POST" class="contact-form-inner">
                        @csrf
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name *</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                       id="email" name="email" value="{{ old('email') }}" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                       id="phone" name="phone" value="{{ old('phone') }}">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="subject" class="form-label">Subject</label>
                                <input type="text" class="form-control @error('subject') is-invalid @enderror" 
                                       id="subject" name="subject" value="{{ old('subject') }}">
                                @error('subject')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="project" class="form-label">Project Type</label>
                            <select class="form-select @error('project') is-invalid @enderror" id="project" name="project">
                                <option value="">Select a service...</option>
                                <option value="Web Development" {{ old('project') == 'Web Development' ? 'selected' : '' }}>Web Development</option>
                                <option value="Mobile App Development" {{ old('project') == 'Mobile App Development' ? 'selected' : '' }}>Mobile App Development</option>
                                <option value="Software Development" {{ old('project') == 'Software Development' ? 'selected' : '' }}>Software Development</option>
                                <option value="IT Consulting" {{ old('project') == 'IT Consulting' ? 'selected' : '' }}>IT Consulting</option>
                                <option value="Other" {{ old('project') == 'Other' ? 'selected' : '' }}>Other</option>
                            </select>
                            @error('project')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-4">
                            <label for="message" class="form-label">Message *</label>
                            <textarea class="form-control @error('message') is-invalid @enderror" 
                                      id="message" name="message" rows="5" required>{{ old('message') }}</textarea>
                            @error('message')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="text-start">
                            <button type="submit" class="btn btn-contact btn-lg text-white">
                                <i class="fas fa-paper-plane me-2"></i>Send Message
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="contact-info-card p-4 h-100">
                    <h3 class="h5 mb-4">Why Choose GrandTek?</h3>
                    <div class="mb-3">
                        <i class="fas fa-check-circle text-primary me-2"></i>
                        <strong>Expert Team:</strong> Experienced developers and consultants
                    </div>
                    <div class="mb-3">
                        <i class="fas fa-check-circle text-primary me-2"></i>
                        <strong>Quality Assurance:</strong> Rigorous testing and quality control
                    </div>
                    <div class="mb-3">
                        <i class="fas fa-check-circle text-primary me-2"></i>
                        <strong>Timely Delivery:</strong> Projects completed on schedule
                    </div>
                    <div class="mb-3">
                        <i class="fas fa-check-circle text-primary me-2"></i>
                        <strong>24/7 Support:</strong> Ongoing maintenance and support
                    </div>
                    <div class="mb-4">
                        <i class="fas fa-check-circle text-primary me-2"></i>
                        <strong>Competitive Pricing:</strong> Best value for your investment
                    </div>
                    
                    <hr class="my-4">
                    
                    <h4 class="h6 mb-3">Business Hours</h4>
                    <div class="small text-muted">
                        <div class="d-flex justify-content-between mb-1">
                            <span>Monday - Friday:</span>
                            <span>8:00 AM - 6:00 PM</span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span>Saturday:</span>
                            <span>9:00 AM - 2:00 PM</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Sunday:</span>
                            <span>Closed</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
    // Auto-dismiss alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
</script>
@endpush
