@extends('backend.layouts.app')

@section('title', 'View Project')

@section('content')
<!-- <PERSON> Header -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Project Details</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('projects.index') }}">Projects</a></li>
                    <li class="breadcrumb-item active">{{ $project->title }}</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <a href="{{ route('projects.index') }}" class="btn btn-secondary">
                    <i class="ri-arrow-left-line me-1"></i>Back to Projects
                </a>
            </div>
            <div>
                <a href="{{ route('projects.ratings', $project->id) }}" class="btn btn-outline-warning">
                    <i class="ri-star-line me-1"></i>Ratings ({{ $project->total_ratings }})
                </a>
                <a href="{{ route('projects.edit', $project->id) }}" class="btn btn-primary">
                    <i class="ri-edit-line me-1"></i>Edit Project
                </a>
                @if($project->status === 'active')
                    <a href="{{ route('project.detail', $project->slug) }}" class="btn btn-outline-info" target="_blank">
                        <i class="ri-external-link-line me-1"></i>View Frontend
                    </a>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Project Information -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Project Information</h4>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Title:</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        {{ $project->title }}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Slug:</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        <code>{{ $project->slug }}</code>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Status:</h6>
                    </div>
                    <div class="col-sm-9">
                        @php
                            $statusColors = [
                                'active' => 'success',
                                'inactive' => 'secondary',
                                'completed' => 'info',
                                'in_progress' => 'warning'
                            ];
                        @endphp
                        <span class="badge bg-{{ $statusColors[$project->status] ?? 'secondary' }}">
                            {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                        </span>
                    </div>
                </div>

                @if($project->category)
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Category:</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        {{ $project->category }}
                    </div>
                </div>
                @endif

                @if($project->client_name)
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Client:</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        {{ $project->client_name }}
                    </div>
                </div>
                @endif

                @if($project->project_url)
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Project URL:</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        <a href="{{ $project->project_url }}" target="_blank" class="text-decoration-none">
                            {{ $project->project_url }} <i class="ri-external-link-line"></i>
                        </a>
                    </div>
                </div>
                @endif

                @if($project->start_date || $project->end_date)
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Duration:</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        @if($project->start_date)
                            {{ $project->start_date->format('M d, Y') }}
                        @endif
                        @if($project->start_date && $project->end_date)
                            -
                        @endif
                        @if($project->end_date)
                            {{ $project->end_date->format('M d, Y') }}
                        @endif
                    </div>
                </div>
                @endif

                @if($project->technologies && count($project->technologies) > 0)
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Technologies:</h6>
                    </div>
                    <div class="col-sm-9">
                        @foreach($project->technologies as $tech)
                            <span class="badge bg-primary me-1 mb-1">{{ $tech }}</span>
                        @endforeach
                    </div>
                </div>
                @endif

                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Featured:</h6>
                    </div>
                    <div class="col-sm-9">
                        @if($project->featured)
                            <span class="badge bg-success">Yes</span>
                        @else
                            <span class="badge bg-secondary">No</span>
                        @endif
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Created:</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        {{ $project->created_at->format('M d, Y \a\t g:i A') }}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Last Updated:</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        {{ $project->updated_at->format('M d, Y \a\t g:i A') }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Descriptions -->
        <div class="card mt-4">
            <div class="card-header">
                <h4 class="card-title mb-0">Descriptions</h4>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h6>Short Description:</h6>
                    <p class="text-muted">{{ $project->description }}</p>
                </div>

                @if($project->short_description)
                <div class="mb-4">
                    <h6>Summary:</h6>
                    <p class="text-muted">{{ $project->short_description }}</p>
                </div>
                @endif

                @if($project->full_description)
                <div class="mb-4">
                    <h6>Full Description:</h6>
                    <div class="text-muted">{!! nl2br(e($project->full_description)) !!}</div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Project Image -->
        @if($project->project_image_url)
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Main Image</h5>
            </div>
            <div class="card-body">
                <img src="{{ $project->project_image_url }}" class="img-fluid rounded" alt="{{ $project->title }}">
            </div>
        </div>
        @endif

        <!-- Gallery Images -->
        @if($project->gallery_images && count($project->gallery_images) > 0)
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Gallery Images</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($project->gallery_images as $image)
                        <div class="col-6 mb-3">
                            <img src="{{ Storage::url($image) }}" class="img-fluid rounded" alt="Gallery Image">
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
        @endif

        <!-- SEO Information -->
        @if($project->meta_title || $project->meta_description || $project->meta_keywords)
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">SEO Information</h5>
            </div>
            <div class="card-body">
                @if($project->meta_title)
                <div class="mb-3">
                    <h6>Meta Title:</h6>
                    <p class="text-muted small">{{ $project->meta_title }}</p>
                </div>
                @endif

                @if($project->meta_description)
                <div class="mb-3">
                    <h6>Meta Description:</h6>
                    <p class="text-muted small">{{ $project->meta_description }}</p>
                </div>
                @endif

                @if($project->meta_keywords)
                <div class="mb-3">
                    <h6>Meta Keywords:</h6>
                    <p class="text-muted small">{{ $project->meta_keywords }}</p>
                </div>
                @endif
            </div>
        </div>
        @endif
    </div>
</div>
@endsection
