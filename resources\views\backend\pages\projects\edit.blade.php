@extends('backend.layouts.app')

@section('title', 'Edit Project')

@section('content')
<!-- <PERSON> Header -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Edit Project</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('projects.index') }}">Projects</a></li>
                    <li class="breadcrumb-item active">Edit Project</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Project Form -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Project Information</h4>
            </div>
            <div class="card-body">
                <form action="{{ route('projects.update', $project->id) }}" method="POST" enctype="multipart/form-data" id="projectForm">
                    @csrf
                    @method('PUT')

                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Basic Information</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="title" class="form-label">Project Title <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                                       id="title" name="title" value="{{ old('title', $project->title) }}" required>
                                                @error('title')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="slug" class="form-label">Slug</label>
                                                <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                                                       id="slug" name="slug" value="{{ old('slug', $project->slug) }}">
                                                <div class="form-text">Leave empty to auto-generate from title</div>
                                                @error('slug')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="description" class="form-label">Short Description <span class="text-danger">*</span></label>
                                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                                          id="description" name="description" rows="3" required>{{ old('description', $project->description) }}</textarea>
                                                <div class="form-text">Brief description for project cards and listings (max 500 characters)</div>
                                                @error('description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="short_description" class="form-label">Summary</label>
                                                <textarea class="form-control @error('short_description') is-invalid @enderror" 
                                                          id="short_description" name="short_description" rows="2">{{ old('short_description', $project->short_description) }}</textarea>
                                                <div class="form-text">Very brief summary for homepage display (max 300 characters)</div>
                                                @error('short_description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="full_description" class="form-label">Full Description</label>
                                                <textarea class="form-control @error('full_description') is-invalid @enderror" 
                                                          id="full_description" name="full_description" rows="8">{{ old('full_description', $project->full_description) }}</textarea>
                                                <div class="form-text">Detailed description for project detail page</div>
                                                @error('full_description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Project Settings</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                        <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                            <option value="">Select Status</option>
                                            <option value="active" {{ old('status', $project->status) == 'active' ? 'selected' : '' }}>Active</option>
                                            <option value="inactive" {{ old('status', $project->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                            <option value="completed" {{ old('status', $project->status) == 'completed' ? 'selected' : '' }}>Completed</option>
                                            <option value="in_progress" {{ old('status', $project->status) == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                        </select>
                                        @error('status')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="mb-3">
                                        <label for="category" class="form-label">Category</label>
                                        <input type="text" class="form-control @error('category') is-invalid @enderror" 
                                               id="category" name="category" value="{{ old('category', $project->category) }}">
                                        @error('category')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="featured" name="featured" value="1" 
                                                   {{ old('featured', $project->featured) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="featured">
                                                Featured Project
                                            </label>
                                        </div>
                                        <div class="form-text">Featured projects appear prominently on homepage</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Project Details -->
                    <div class="row mt-4">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Project Details</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="client_name" class="form-label">Client Name</label>
                                                <input type="text" class="form-control @error('client_name') is-invalid @enderror"
                                                       id="client_name" name="client_name" value="{{ old('client_name', $project->client_name) }}">
                                                @error('client_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="project_url" class="form-label">Project URL</label>
                                                <input type="url" class="form-control @error('project_url') is-invalid @enderror"
                                                       id="project_url" name="project_url" value="{{ old('project_url', $project->project_url) }}">
                                                @error('project_url')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="start_date" class="form-label">Start Date</label>
                                                <input type="date" class="form-control @error('start_date') is-invalid @enderror"
                                                       id="start_date" name="start_date" value="{{ old('start_date', $project->start_date?->format('Y-m-d')) }}">
                                                @error('start_date')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="end_date" class="form-label">End Date</label>
                                                <input type="date" class="form-control @error('end_date') is-invalid @enderror"
                                                       id="end_date" name="end_date" value="{{ old('end_date', $project->end_date?->format('Y-m-d')) }}">
                                                @error('end_date')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="technologies_input" class="form-label">Technologies Used</label>
                                                <input type="text" class="form-control" id="technologies_input"
                                                       placeholder="Type and press Enter to add technologies">
                                                <input type="hidden" name="technologies" id="technologies_hidden"
                                                       value="{{ old('technologies', json_encode($project->technologies ?? [])) }}">
                                                <div class="form-text">Press Enter after typing each technology</div>
                                                <div id="technologies_display" class="mt-2"></div>
                                                @error('technologies')
                                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Project Media</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="project_image" class="form-label">Main Project Image</label>
                                        @if($project->project_image_url)
                                            <div class="mb-2">
                                                <img src="{{ $project->project_image_url }}" class="img-thumbnail" style="max-width: 200px;">
                                                <div class="form-text">Current image</div>
                                            </div>
                                        @endif
                                        <input type="file" class="form-control @error('project_image') is-invalid @enderror"
                                               id="project_image" name="project_image" accept="image/*">
                                        <div class="form-text">Upload a new image to replace current one</div>
                                        @error('project_image')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="mb-3">
                                        <label for="gallery_images" class="form-label">Gallery Images</label>
                                        @if($project->gallery_images && count($project->gallery_images) > 0)
                                            <div class="mb-2">
                                                <div class="row">
                                                    @foreach($project->gallery_images as $index => $image)
                                                        <div class="col-6 mb-2">
                                                            <div class="position-relative">
                                                                <img src="{{ Storage::url($image) }}" class="img-thumbnail w-100">
                                                                <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0"
                                                                        onclick="removeGalleryImage({{ $project->id }}, {{ $index }})">
                                                                    <i class="ri-close-line"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endif
                                        <input type="file" class="form-control @error('gallery_images') is-invalid @enderror"
                                               id="gallery_images" name="gallery_images[]" accept="image/*" multiple>
                                        <div class="form-text">Select multiple images for project gallery</div>
                                        @error('gallery_images')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SEO Settings -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">SEO Settings</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label for="meta_title" class="form-label">Meta Title</label>
                                                <input type="text" class="form-control @error('meta_title') is-invalid @enderror"
                                                       id="meta_title" name="meta_title" value="{{ old('meta_title', $project->meta_title) }}">
                                                <div class="form-text">Leave empty to use project title</div>
                                                @error('meta_title')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="meta_description" class="form-label">Meta Description</label>
                                                <textarea class="form-control @error('meta_description') is-invalid @enderror"
                                                          id="meta_description" name="meta_description" rows="3">{{ old('meta_description', $project->meta_description) }}</textarea>
                                                <div class="form-text">Leave empty to use short description</div>
                                                @error('meta_description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="meta_keywords" class="form-label">Meta Keywords</label>
                                                <textarea class="form-control @error('meta_keywords') is-invalid @enderror"
                                                          id="meta_keywords" name="meta_keywords" rows="3">{{ old('meta_keywords', $project->meta_keywords) }}</textarea>
                                                <div class="form-text">Comma-separated keywords</div>
                                                @error('meta_keywords')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="{{ route('projects.index') }}" class="btn btn-secondary">
                                    <i class="ri-arrow-left-line me-1"></i>Back to Projects
                                </a>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ri-save-line me-1"></i>Update Project
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Technologies management
    let technologies = [];
    const technologiesInput = document.getElementById('technologies_input');
    const technologiesHidden = document.getElementById('technologies_hidden');
    const technologiesDisplay = document.getElementById('technologies_display');

    // Load existing technologies
    try {
        const existingTech = JSON.parse(technologiesHidden.value || '[]');
        technologies = Array.isArray(existingTech) ? existingTech : [];
        updateTechnologiesDisplay();
    } catch (e) {
        technologies = [];
    }

    // Add technology on Enter key
    technologiesInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const tech = this.value.trim();
            if (tech && !technologies.includes(tech)) {
                technologies.push(tech);
                updateTechnologiesDisplay();
                this.value = '';
            }
        }
    });

    function updateTechnologiesDisplay() {
        technologiesHidden.value = JSON.stringify(technologies);
        technologiesDisplay.innerHTML = technologies.map((tech, index) =>
            `<span class="badge bg-primary me-1 mb-1">
                ${tech}
                <button type="button" class="btn-close btn-close-white ms-1" onclick="removeTechnology(${index})"></button>
            </span>`
        ).join('');
    }

    // Make removeTechnology globally available
    window.removeTechnology = function(index) {
        technologies.splice(index, 1);
        updateTechnologiesDisplay();
    };

    // Auto-generate slug from title
    document.getElementById('title').addEventListener('input', function() {
        const slugField = document.getElementById('slug');
        if (!slugField.value) {
            slugField.value = this.value.toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/^-+|-+$/g, '');
        }
    });

    // Form validation
    document.getElementById('projectForm').addEventListener('submit', function(e) {
        const title = document.getElementById('title').value.trim();
        const description = document.getElementById('description').value.trim();
        const status = document.getElementById('status').value;

        if (!title || !description || !status) {
            e.preventDefault();
            alert('Please fill in all required fields.');
            return false;
        }
    });
});

// Remove gallery image function
function removeGalleryImage(projectId, imageIndex) {
    if (confirm('Are you sure you want to remove this image?')) {
        fetch(`/admin/projects/${projectId}/gallery/${imageIndex}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Accept': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to remove image');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to remove image');
        });
    }
}
</script>
@endsection
