<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class Project extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'short_description',
        'full_description',
        'category',
        'client_name',
        'project_url',
        'technologies',
        'start_date',
        'end_date',
        'project_image',
        'gallery_images',
        'status',
        'featured',
        'sort_order',
        'average_rating',
        'total_ratings',
        'views_count',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    protected $casts = [
        'technologies' => 'array',
        'gallery_images' => 'array',
        'start_date' => 'date',
        'end_date' => 'date',
        'featured' => 'boolean',
        'sort_order' => 'integer',
        'average_rating' => 'decimal:2',
        'total_ratings' => 'integer',
        'views_count' => 'integer',
    ];

    /**
     * Scope to get only active projects
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get only featured projects
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    /**
     * Scope to get projects ordered by sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('created_at', 'desc');
    }

    /**
     * Get the full URL for project image
     */
    public function getProjectImageUrlAttribute()
    {
        if ($this->project_image) {
            // Check if it's already a full URL
            if (filter_var($this->project_image, FILTER_VALIDATE_URL)) {
                return $this->project_image;
            }
            // Check if it's a storage path
            if (Storage::disk('public')->exists($this->project_image)) {
                return Storage::url($this->project_image);
            }
            // Fallback to asset path
            return asset($this->project_image);
        }
        return null;
    }

    /**
     * Get gallery image URLs
     */
    public function getGalleryImageUrlsAttribute()
    {
        if (!$this->gallery_images) {
            return [];
        }

        return collect($this->gallery_images)->map(function ($image) {
            if (filter_var($image, FILTER_VALIDATE_URL)) {
                return $image;
            }
            if (Storage::disk('public')->exists($image)) {
                return Storage::url($image);
            }
            return asset($image);
        })->toArray();
    }

    /**
     * Get status badge HTML
     */
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'active' => 'bg-success',
            'inactive' => 'bg-secondary',
            'completed' => 'bg-primary',
            'in_progress' => 'bg-warning'
        ];

        $class = $badges[$this->status] ?? 'bg-secondary';
        $text = ucfirst(str_replace('_', ' ', $this->status));
        return "<span class='badge {$class}'>{$text}</span>";
    }

    /**
     * Generate slug from title
     */
    public function generateSlug()
    {
        $slug = Str::slug($this->title);
        $originalSlug = $slug;
        $counter = 1;

        while (static::where('slug', $slug)->where('id', '!=', $this->id)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Get all ratings for this project
     */
    public function ratings()
    {
        return $this->hasMany(ProjectRating::class);
    }

    /**
     * Get active ratings for this project
     */
    public function activeRatings()
    {
        return $this->hasMany(ProjectRating::class)->where('status', 'active');
    }

    /**
     * Get featured ratings
     */
    public function featuredRatings()
    {
        return $this->hasMany(ProjectRating::class)->where('is_featured', true)->where('status', 'active');
    }

    /**
     * Calculate and update average rating
     */
    public function updateAverageRating()
    {
        $ratings = $this->activeRatings();
        $average = $ratings->avg('rating') ?? 0;
        $total = $ratings->count();

        $this->update([
            'average_rating' => round($average, 2),
            'total_ratings' => $total
        ]);

        return $this;
    }

    /**
     * Increment views count
     */
    public function incrementViews()
    {
        $this->increment('views_count');
        return $this;
    }

    /**
     * Get rating distribution
     */
    public function getRatingDistribution()
    {
        $distribution = [];
        for ($i = 1; $i <= 5; $i++) {
            $count = $this->activeRatings()->where('rating', $i)->count();
            $percentage = $this->total_ratings > 0 ? round(($count / $this->total_ratings) * 100, 1) : 0;
            $distribution[$i] = [
                'count' => $count,
                'percentage' => $percentage
            ];
        }
        return $distribution;
    }

    /**
     * Get star rating HTML
     */
    public function getStarRatingHtmlAttribute()
    {
        $rating = $this->average_rating;
        $html = '';

        for ($i = 1; $i <= 5; $i++) {
            if ($i <= floor($rating)) {
                $html .= '<i class="fas fa-star text-warning"></i>';
            } elseif ($i <= ceil($rating) && $rating > floor($rating)) {
                $html .= '<i class="fas fa-star-half-alt text-warning"></i>';
            } else {
                $html .= '<i class="far fa-star text-muted"></i>';
            }
        }

        return $html;
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-generate slug when creating
        static::creating(function ($project) {
            if (empty($project->slug)) {
                $project->slug = $project->generateSlug();
            }
            if (is_null($project->sort_order)) {
                $project->sort_order = static::max('sort_order') + 1;
            }
        });

        // Update slug when title changes
        static::updating(function ($project) {
            if ($project->isDirty('title') && empty($project->slug)) {
                $project->slug = $project->generateSlug();
            }
        });

        // Clean up files when deleting
        static::deleting(function ($project) {
            if ($project->project_image && Storage::disk('public')->exists($project->project_image)) {
                Storage::disk('public')->delete($project->project_image);
            }
            if ($project->gallery_images) {
                foreach ($project->gallery_images as $image) {
                    if (Storage::disk('public')->exists($image)) {
                        Storage::disk('public')->delete($image);
                    }
                }
            }
        });
    }

    /**
     * Get route key name for route model binding
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }
}
