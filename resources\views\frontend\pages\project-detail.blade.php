@extends('frontend.layouts.app')

@section('title', $project->meta_title ?? $project->title . ' - Project Details')
@section('description', $project->meta_description ?? $project->short_description)
@section('keywords', $project->meta_keywords ?? $project->title)

@section('content')
<!-- Page Header -->
<div class="container-fluid page-header py-5">
    <div class="container text-center py-5">
        <h1 class="display-2 text-white mb-4 animated slideInDown">{{ $project->title }}</h1>
        <nav aria-label="breadcrumb animated slideInDown">
            <ol class="breadcrumb justify-content-center mb-0">
                <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                <li class="breadcrumb-item"><a href="{{ route('projects.all') }}">Projects</a></li>
                <li class="breadcrumb-item text-white active" aria-current="page">{{ $project->title }}</li>
            </ol>
        </nav>
    </div>
</div>

<!-- Project Detail Section -->
<div class="container-fluid py-5">
    <div class="container">
        <div class="row g-5">
            <!-- Project Images -->
            <div class="col-lg-8">
                <!-- Main Project Image -->
                <div class="project-main-image mb-4 wow fadeIn" data-wow-delay=".3s">
                    @if($project->project_image_url)
                        <img src="{{ $project->project_image_url }}" class="img-fluid w-100 rounded" 
                             alt="{{ $project->title }}" style="height: 400px; object-fit: cover;">
                    @else
                        <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 400px;">
                            <i class="ri-image-line text-muted" style="font-size: 4rem;"></i>
                        </div>
                    @endif
                </div>

                <!-- Project Gallery -->
                @if($project->gallery_image_urls && count($project->gallery_image_urls) > 0)
                    <div class="project-gallery mb-4 wow fadeIn" data-wow-delay=".5s">
                        <h4 class="mb-3">Project Gallery</h4>
                        <div class="row g-3">
                            @foreach($project->gallery_image_urls as $index => $image)
                                <div class="col-md-4 col-sm-6">
                                    <div class="gallery-item">
                                        <img src="{{ $image }}" class="img-fluid w-100 rounded" 
                                             alt="{{ $project->title }} Gallery {{ $index + 1 }}"
                                             style="height: 200px; object-fit: cover; cursor: pointer;"
                                             onclick="openImageModal('{{ $image }}')">
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Project Description -->
                <div class="project-description wow fadeIn" data-wow-delay=".7s">
                    <h4 class="mb-3">Project Overview</h4>
                    @if($project->full_description)
                        <div class="project-content">
                            {!! nl2br(e($project->full_description)) !!}
                        </div>
                    @else
                        <p class="text-muted">{{ $project->description }}</p>
                    @endif
                </div>

                <!-- Project Reviews Section -->
                <div class="project-reviews mt-5 wow fadeIn" data-wow-delay=".9s">
                    <div class="row">
                        <div class="col-lg-6">
                            <h4 class="mb-4">Project Reviews & Ratings</h4>

                            <!-- Rating Summary -->
                            @if($project->total_ratings > 0)
                                <div class="rating-summary mb-4">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="rating-display me-3">
                                            <span class="rating-number">{{ number_format($project->average_rating, 1) }}</span>
                                            <div class="stars">
                                                {!! $project->star_rating_html !!}
                                            </div>
                                        </div>
                                        <div class="rating-text">
                                            <p class="mb-0">Based on {{ $project->total_ratings }} {{ Str::plural('review', $project->total_ratings) }}</p>
                                        </div>
                                    </div>

                                    <!-- Rating Distribution -->
                                    <div class="rating-distribution">
                                        @foreach(array_reverse($ratingDistribution, true) as $star => $data)
                                            <div class="rating-bar d-flex align-items-center mb-2">
                                                <span class="star-label me-2">{{ $star }} <i class="fas fa-star text-warning"></i></span>
                                                <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                                    <div class="progress-bar bg-warning" style="width: {{ $data['percentage'] }}%"></div>
                                                </div>
                                                <span class="count-label">{{ $data['count'] }}</span>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @else
                                <div class="no-ratings mb-4">
                                    <p class="text-muted">No reviews yet. Be the first to review this project!</p>
                                </div>
                            @endif

                            <!-- Rating Form -->
                            <div class="rating-form">
                                <h5 class="mb-3">Leave a Review</h5>
                                <form id="ratingForm">
                                    @csrf
                                    <div class="mb-3">
                                        <label class="form-label">Your Rating <span class="text-danger">*</span></label>
                                        <div class="rating-input">
                                            <input type="hidden" name="rating" id="rating" required>
                                            <div class="stars-input">
                                                @for($i = 1; $i <= 5; $i++)
                                                    <i class="far fa-star" data-rating="{{ $i }}"></i>
                                                @endfor
                                            </div>
                                            <div class="invalid-feedback" id="rating-error"></div>
                                        </div>
                                    </div>

                                    @guest
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="user_name" class="form-label">Your Name <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="user_name" name="user_name" required>
                                                <div class="invalid-feedback" id="user_name-error"></div>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="user_email" class="form-label">Your Email <span class="text-danger">*</span></label>
                                                <input type="email" class="form-control" id="user_email" name="user_email" required>
                                                <div class="invalid-feedback" id="user_email-error"></div>
                                            </div>
                                        </div>
                                    @endguest

                                    <div class="mb-3">
                                        <label for="review" class="form-label">Your Review</label>
                                        <textarea class="form-control" id="review" name="review" rows="4"
                                                  placeholder="Share your thoughts about this project..."></textarea>
                                        <div class="invalid-feedback" id="review-error"></div>
                                    </div>

                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-2"></i>Submit Review
                                    </button>
                                </form>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <!-- Recent Reviews -->
                            @if($ratings->count() > 0)
                                <h5 class="mb-3">Recent Reviews</h5>
                                <div class="reviews-list">
                                    @foreach($ratings as $rating)
                                        <div class="review-item mb-4 p-3 border rounded">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <div>
                                                    <h6 class="mb-1">{{ $rating->display_name }}</h6>
                                                    <div class="stars">
                                                        @for($i = 1; $i <= 5; $i++)
                                                            @if($i <= $rating->rating)
                                                                <i class="fas fa-star text-warning"></i>
                                                            @else
                                                                <i class="far fa-star text-muted"></i>
                                                            @endif
                                                        @endfor
                                                    </div>
                                                </div>
                                                <small class="text-muted">{{ $rating->created_at->diffForHumans() }}</small>
                                            </div>
                                            @if($rating->review)
                                                <p class="mb-0 text-muted">{{ $rating->review }}</p>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>

                                <!-- Pagination -->
                                @if($ratings->hasPages())
                                    <div class="d-flex justify-content-center">
                                        {{ $ratings->links() }}
                                    </div>
                                @endif
                            @else
                                <div class="no-reviews text-center py-4">
                                    <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No reviews yet for this project.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project Info Sidebar -->
            <div class="col-lg-4">
                <div class="project-info-sidebar">
                    <!-- Project Details Card -->
                    <div class="card mb-4 wow fadeIn" data-wow-delay=".3s">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="ri-information-line me-2"></i>Project Details</h5>
                        </div>
                        <div class="card-body">
                            @if($project->client_name)
                                <div class="detail-item mb-3">
                                    <strong><i class="ri-user-line me-2 text-primary"></i>Client:</strong>
                                    <span class="ms-2">{{ $project->client_name }}</span>
                                </div>
                            @endif

                            @if($project->category)
                                <div class="detail-item mb-3">
                                    <strong><i class="ri-folder-line me-2 text-primary"></i>Category:</strong>
                                    <span class="ms-2">{{ $project->category }}</span>
                                </div>
                            @endif

                            @if($project->start_date)
                                <div class="detail-item mb-3">
                                    <strong><i class="ri-calendar-line me-2 text-primary"></i>Duration:</strong>
                                    <span class="ms-2">
                                        {{ $project->start_date->format('M Y') }}
                                        @if($project->end_date)
                                            - {{ $project->end_date->format('M Y') }}
                                        @else
                                            - Present
                                        @endif
                                    </span>
                                </div>
                            @endif

                            <div class="detail-item mb-3">
                                <strong><i class="ri-flag-line me-2 text-primary"></i>Status:</strong>
                                <span class="ms-2">
                                    @switch($project->status)
                                        @case('completed')
                                            <span class="badge bg-success">Completed</span>
                                            @break
                                        @case('in_progress')
                                            <span class="badge bg-warning">In Progress</span>
                                            @break
                                        @case('active')
                                            <span class="badge bg-primary">Active</span>
                                            @break
                                        @default
                                            <span class="badge bg-secondary">{{ ucfirst($project->status) }}</span>
                                    @endswitch
                                </span>
                            </div>

                            @if($project->project_url)
                                <div class="detail-item mb-0">
                                    <a href="{{ $project->project_url }}" target="_blank" class="btn btn-primary w-100">
                                        <i class="ri-external-link-line me-2"></i>View Live Project
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Technologies Used -->
                    @if($project->technologies && count($project->technologies) > 0)
                        <div class="card mb-4 wow fadeIn" data-wow-delay=".5s">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="ri-code-line me-2"></i>Technologies Used</h5>
                            </div>
                            <div class="card-body">
                                <div class="technologies-list">
                                    @foreach($project->technologies as $tech)
                                        <span class="badge bg-light text-dark me-2 mb-2">{{ $tech }}</span>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Contact CTA -->
                    <div class="card wow fadeIn" data-wow-delay=".7s">
                        <div class="card-body text-center">
                            <h5 class="card-title">Interested in Similar Project?</h5>
                            <p class="card-text text-muted">Let's discuss how we can help bring your ideas to life.</p>
                            <a href="{{ route('home') }}" class="btn btn-primary">
                                <i class="ri-mail-line me-2"></i>Get In Touch
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Related Projects Section -->
<div class="container-fluid bg-light py-5">
    <div class="container">
        <div class="text-center mx-auto pb-5 wow fadeIn" data-wow-delay=".3s" style="max-width: 600px;">
            <h5 class="text-primary">More Projects</h5>
            <h1>Other Projects You Might Like</h1>
        </div>
        
        <!-- This would be populated with related projects -->
        <div class="row g-4">
            <div class="col-12 text-center">
                <a href="{{ route('projects.all') }}" class="btn btn-primary btn-lg">
                    <i class="ri-eye-line me-2"></i>View All Projects
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ $project->title }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <img id="modalImage" src="" class="img-fluid w-100" alt="">
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.project-content {
    line-height: 1.8;
    font-size: 1.1rem;
}

.detail-item {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 0.75rem;
}

.detail-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.technologies-list .badge {
    font-size: 0.85rem;
    padding: 0.5rem 0.75rem;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.project-info-sidebar .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.project-info-sidebar .card-header {
    border-bottom: none;
    font-weight: 600;
}

/* Rating Styles */
.rating-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #ffc107;
    line-height: 1;
}

.rating-display .stars {
    font-size: 1.2rem;
}

.rating-bar {
    font-size: 0.9rem;
}

.star-label {
    min-width: 60px;
}

.count-label {
    min-width: 30px;
    text-align: right;
    font-size: 0.85rem;
    color: #6c757d;
}

.stars-input {
    font-size: 1.5rem;
    cursor: pointer;
}

.stars-input i {
    margin-right: 0.25rem;
    transition: color 0.2s ease;
}

.stars-input i:hover,
.stars-input i.active {
    color: #ffc107 !important;
}

.review-item {
    background-color: #f8f9fa;
    transition: transform 0.2s ease;
}

.review-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.no-reviews {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
}

@media (max-width: 768px) {
    .project-main-image img,
    .project-main-image .bg-light {
        height: 250px !important;
    }

    .gallery-item img {
        height: 150px !important;
    }

    .rating-number {
        font-size: 2rem;
    }

    .project-reviews .col-lg-6:first-child {
        margin-bottom: 2rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
function openImageModal(imageSrc) {
    document.getElementById('modalImage').src = imageSrc;
    const modal = new bootstrap.Modal(document.getElementById('imageModal'));
    modal.show();
}

// Rating System JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const starsInput = document.querySelectorAll('.stars-input i');
    const ratingInput = document.getElementById('rating');
    const ratingForm = document.getElementById('ratingForm');

    // Star rating interaction
    starsInput.forEach((star, index) => {
        star.addEventListener('mouseover', function() {
            highlightStars(index + 1);
        });

        star.addEventListener('click', function() {
            const rating = index + 1;
            ratingInput.value = rating;
            setActiveStars(rating);
            clearError('rating');
        });
    });

    // Reset stars on mouse leave
    document.querySelector('.stars-input').addEventListener('mouseleave', function() {
        const currentRating = ratingInput.value;
        if (currentRating) {
            setActiveStars(currentRating);
        } else {
            resetStars();
        }
    });

    function highlightStars(rating) {
        starsInput.forEach((star, index) => {
            if (index < rating) {
                star.className = 'fas fa-star text-warning';
            } else {
                star.className = 'far fa-star text-muted';
            }
        });
    }

    function setActiveStars(rating) {
        starsInput.forEach((star, index) => {
            if (index < rating) {
                star.className = 'fas fa-star text-warning active';
            } else {
                star.className = 'far fa-star text-muted';
            }
        });
    }

    function resetStars() {
        starsInput.forEach(star => {
            star.className = 'far fa-star text-muted';
        });
    }

    function showError(field, message) {
        const input = document.getElementById(field);
        const errorDiv = document.getElementById(field + '-error');

        if (input) {
            input.classList.add('is-invalid');
        }
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
    }

    function clearError(field) {
        const input = document.getElementById(field);
        const errorDiv = document.getElementById(field + '-error');

        if (input) {
            input.classList.remove('is-invalid');
        }
        if (errorDiv) {
            errorDiv.textContent = '';
            errorDiv.style.display = 'none';
        }
    }

    function clearAllErrors() {
        const errorFields = ['rating', 'user_name', 'user_email', 'review'];
        errorFields.forEach(field => clearError(field));
    }

    // Form submission
    ratingForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        // Clear previous errors
        clearAllErrors();

        // Validate rating
        if (!ratingInput.value) {
            showError('rating', 'Please select a rating');
            return;
        }

        // Disable submit button
        submitBtn.disabled = true;
        submitBtn.textContent = 'Submitting...';

        // Submit rating
        fetch('{{ route("project.rate", $project->slug) }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                alert('Thank you for your review! Your rating has been submitted successfully.');

                // Reset form
                ratingForm.reset();
                ratingInput.value = '';
                resetStars();

                // Reload page to show new review
                window.location.reload();
            } else {
                // Handle validation errors
                if (data.errors) {
                    Object.keys(data.errors).forEach(field => {
                        showError(field, data.errors[field][0]);
                    });
                } else {
                    alert(data.message || 'An error occurred while submitting your review.');
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while submitting your review. Please try again.');
        })
        .finally(() => {
            // Re-enable submit button
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    });
});
</script>
@endpush
