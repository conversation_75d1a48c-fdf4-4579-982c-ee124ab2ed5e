<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->decimal('average_rating', 3, 2)->default(0)->after('sort_order');
            $table->integer('total_ratings')->default(0)->after('average_rating');
            $table->integer('views_count')->default(0)->after('total_ratings');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->dropColumn(['average_rating', 'total_ratings', 'views_count']);
        });
    }
};
