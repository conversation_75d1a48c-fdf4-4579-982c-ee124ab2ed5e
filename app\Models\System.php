<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class System extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'short_description',
        'full_description',
        'category',
        'technologies',
        'system_url',
        'documentation_url',
        'system_image',
        'gallery_images',
        'features',
        'status',
        'featured',
        'sort_order',
        'average_rating',
        'total_ratings',
        'views_count',
        'launch_date',
        'version',
        'changelog',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    protected $casts = [
        'technologies' => 'array',
        'gallery_images' => 'array',
        'features' => 'array',
        'featured' => 'boolean',
        'sort_order' => 'integer',
        'average_rating' => 'decimal:2',
        'total_ratings' => 'integer',
        'views_count' => 'integer',
        'launch_date' => 'date',
    ];

    /**
     * Scope to get only active systems
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get only featured systems
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    /**
     * Scope to get systems ordered by sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('created_at', 'desc');
    }

    /**
     * Scope to get systems by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get the full URL for system image
     */
    public function getSystemImageUrlAttribute()
    {
        if ($this->system_image) {
            // Check if it's already a full URL
            if (filter_var($this->system_image, FILTER_VALIDATE_URL)) {
                return $this->system_image;
            }
            // Check if it's a storage path
            if (Storage::disk('public')->exists($this->system_image)) {
                return Storage::url($this->system_image);
            }
            // Fallback to asset path
            return asset($this->system_image);
        }
        return null;
    }

    /**
     * Get gallery image URLs
     */
    public function getGalleryImageUrlsAttribute()
    {
        if (!$this->gallery_images) {
            return [];
        }

        return collect($this->gallery_images)->map(function ($image) {
            if (filter_var($image, FILTER_VALIDATE_URL)) {
                return $image;
            }
            if (Storage::disk('public')->exists($image)) {
                return Storage::url($image);
            }
            return asset($image);
        })->toArray();
    }

    /**
     * Get the route key for the model
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Relationship with system ratings
     */
    public function ratings()
    {
        return $this->hasMany(SystemRating::class);
    }

    /**
     * Get active ratings
     */
    public function activeRatings()
    {
        return $this->hasMany(SystemRating::class)->where('status', 'active');
    }

    /**
     * Get featured ratings
     */
    public function featuredRatings()
    {
        return $this->hasMany(SystemRating::class)->where('is_featured', true)->where('status', 'active');
    }

    /**
     * Calculate and update average rating
     */
    public function updateAverageRating()
    {
        $ratings = $this->activeRatings();
        $average = $ratings->avg('rating') ?? 0;
        $total = $ratings->count();

        $this->update([
            'average_rating' => round($average, 2),
            'total_ratings' => $total
        ]);

        return $this;
    }

    /**
     * Increment views count
     */
    public function incrementViews()
    {
        $this->increment('views_count');
        return $this;
    }

    /**
     * Get rating distribution
     */
    public function getRatingDistribution()
    {
        $distribution = [];
        for ($i = 1; $i <= 5; $i++) {
            $distribution[$i] = $this->activeRatings()->where('rating', $i)->count();
        }
        return $distribution;
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-generate slug when creating
        static::creating(function ($system) {
            if (empty($system->slug)) {
                $system->slug = Str::slug($system->title);
            }
            if (is_null($system->sort_order)) {
                $system->sort_order = static::max('sort_order') + 1;
            }
        });

        // Update slug when title changes
        static::updating(function ($system) {
            if ($system->isDirty('title') && empty($system->slug)) {
                $system->slug = Str::slug($system->title);
            }
        });

        // Clean up files when deleting
        static::deleting(function ($system) {
            if ($system->system_image && Storage::disk('public')->exists($system->system_image)) {
                Storage::disk('public')->delete($system->system_image);
            }
            if ($system->gallery_images) {
                foreach ($system->gallery_images as $image) {
                    if (Storage::disk('public')->exists($image)) {
                        Storage::disk('public')->delete($image);
                    }
                }
            }
        });
    }
}
