<?php $__env->startSection('title', 'Our Projects - Portfolio'); ?>
<?php $__env->startSection('description', 'Explore our portfolio of completed projects and see how we help businesses achieve their goals through innovative solutions.'); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<div class="container-fluid page-header py-5">
    <div class="container text-center py-5">
        <h1 class="display-2 text-white mb-4 animated slideInDown">Our Projects</h1>
        <nav aria-label="breadcrumb animated slideInDown">
            <ol class="breadcrumb justify-content-center mb-0">
                <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">Home</a></li>
                <li class="breadcrumb-item text-white active" aria-current="page">Projects</li>
            </ol>
        </nav>
    </div>
</div>

<!-- Projects Section -->
<div class="container-fluid py-5">
    <div class="container">
        <div class="text-center mx-auto pb-5 wow fadeIn" data-wow-delay=".3s" style="max-width: 600px;">
            <h5 class="text-primary">Portfolio</h5>
            <h1>Our Complete Project Portfolio</h1>
            <p class="text-muted">Discover the diverse range of projects we've completed for our clients across various industries and technologies.</p>
        </div>

        <?php if($projects && $projects->count() > 0): ?>
            <!-- Projects Grid -->
            <div class="row g-4">
                <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-xl-4 col-lg-6 col-md-6 wow fadeIn" data-wow-delay="<?php echo e(0.3 + ($index * 0.1)); ?>s">
                        <div class="project-item h-100">
                            <div class="project-img position-relative">
                                <?php if($project->project_image_url): ?>
                                    <img src="<?php echo e($project->project_image_url); ?>" class="img-fluid w-100 rounded" 
                                         alt="<?php echo e($project->title); ?>" style="height: 250px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="img-fluid w-100 rounded bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                                        <i class="ri-image-line text-muted" style="font-size: 3rem;"></i>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Project Overlay -->
                                <div class="project-content">
                                    <a href="<?php echo e(route('project.detail', $project->slug)); ?>" class="text-center">
                                        <h4 class="text-secondary"><?php echo e($project->title); ?></h4>
                                        <p class="m-0 text-white"><?php echo e($project->category ?? $project->short_description); ?></p>
                                    </a>
                                </div>
                                
                                <!-- Project Info -->
                                <div class="project-info position-absolute bottom-0 start-0 end-0 p-3 bg-white bg-opacity-95 rounded-bottom">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1"><?php echo e($project->title); ?></h6>
                                            <?php if($project->category): ?>
                                                <small class="text-muted"><?php echo e($project->category); ?></small>
                                            <?php endif; ?>
                                        </div>
                                        <div class="text-end">
                                            <?php if($project->featured): ?>
                                                <span class="badge bg-warning mb-1"><i class="ri-star-line"></i></span>
                                            <?php endif; ?>
                                            <?php switch($project->status):
                                                case ('completed'): ?>
                                                    <span class="badge bg-success">Completed</span>
                                                    <?php break; ?>
                                                <?php case ('in_progress'): ?>
                                                    <span class="badge bg-warning">In Progress</span>
                                                    <?php break; ?>
                                                <?php default: ?>
                                                    <span class="badge bg-primary"><?php echo e(ucfirst($project->status)); ?></span>
                                            <?php endswitch; ?>
                                        </div>
                                    </div>
                                    
                                    <?php if($project->technologies && count($project->technologies) > 0): ?>
                                        <div class="mt-2">
                                            <?php $__currentLoopData = array_slice($project->technologies, 0, 3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tech): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="badge bg-light text-dark me-1"><?php echo e($tech); ?></span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php if(count($project->technologies) > 3): ?>
                                                <span class="badge bg-secondary">+<?php echo e(count($project->technologies) - 3); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="mt-2">
                                        <a href="<?php echo e(route('project.detail', $project->slug)); ?>" class="btn btn-primary btn-sm">
                                            <i class="ri-eye-line me-1"></i>View Details
                                        </a>
                                        <?php if($project->project_url): ?>
                                            <a href="<?php echo e($project->project_url); ?>" target="_blank" class="btn btn-outline-primary btn-sm ms-1">
                                                <i class="ri-external-link-line me-1"></i>Live Demo
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Pagination -->
            <?php if($projects->hasPages()): ?>
                <div class="row mt-5">
                    <div class="col-12">
                        <div class="d-flex justify-content-center">
                            <?php echo e($projects->links()); ?>

                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <!-- No Projects Found -->
            <div class="row">
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="ri-folder-open-line text-muted" style="font-size: 5rem;"></i>
                        <h3 class="mt-4 mb-3">No Projects Available</h3>
                        <p class="text-muted mb-4">We're currently working on exciting new projects. Check back soon to see our latest work!</p>
                        <a href="<?php echo e(route('home')); ?>" class="btn btn-primary">
                            <i class="ri-mail-line me-2"></i>Get In Touch
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Call to Action Section -->
<div class="container-fluid bg-primary py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="text-white">
                    <h2 class="mb-3">Ready to Start Your Project?</h2>
                    <p class="mb-0">Let's discuss how we can help bring your ideas to life with our expertise and innovative solutions.</p>
                </div>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="<?php echo e(route('home')); ?>" class="btn btn-light btn-lg">
                    <i class="ri-rocket-line me-2"></i>Start Your Project
                </a>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.project-item {
    position: relative;
    overflow: hidden;
    border-radius: 1rem;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.project-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.project-img {
    position: relative;
    overflow: hidden;
}

.project-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    border-radius: 1rem 1rem 0 0;
}

.project-item:hover .project-content {
    opacity: 1;
}

.project-info {
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.project-item:hover .project-info {
    transform: translateY(0);
}

.project-content a {
    text-decoration: none;
    color: white;
}

.project-content h4 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.project-content p {
    font-size: 1rem;
    opacity: 0.9;
}

.badge {
    font-size: 0.75rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .project-item {
        margin-bottom: 2rem;
    }
    
    .project-info {
        position: static !important;
        transform: none !important;
        background: white !important;
        border-radius: 0 0 1rem 1rem !important;
    }
    
    .project-content {
        position: static;
        opacity: 1;
        background: rgba(0, 0, 0, 0.5);
        padding: 1rem;
        border-radius: 0;
    }
}

/* Pagination styling */
.pagination {
    --bs-pagination-color: var(--bs-primary);
    --bs-pagination-hover-color: var(--bs-primary);
    --bs-pagination-focus-color: var(--bs-primary);
    --bs-pagination-active-bg: var(--bs-primary);
    --bs-pagination-active-border-color: var(--bs-primary);
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\Grandtek\resources\views/frontend/pages/projects.blade.php ENDPATH**/ ?>