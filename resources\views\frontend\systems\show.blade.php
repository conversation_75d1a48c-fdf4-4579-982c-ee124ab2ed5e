@extends('frontend.layouts.app')

@section('title', $system->title . ' - System Details')
@section('meta_description', $system->meta_description ?: $system->description)
@section('meta_keywords', $system->meta_keywords)

@section('content')
<div class="container-fluid py-5">
    <div class="container">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                <li class="breadcrumb-item"><a href="{{ route('systems.all') }}">Systems</a></li>
                <li class="breadcrumb-item active">{{ $system->title }}</li>
            </ol>
        </nav>

        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- System Header -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div>
                                <h1 class="h2 mb-2">{{ $system->title }}</h1>
                                <div class="d-flex align-items-center gap-3 mb-3">
                                    @if($system->category)
                                        <span class="badge bg-primary">{{ $system->category }}</span>
                                    @endif
                                    @if($system->version)
                                        <span class="badge bg-secondary">v{{ $system->version }}</span>
                                    @endif
                                    @switch($system->status)
                                        @case('active')
                                            <span class="badge bg-success">Live</span>
                                            @break
                                        @case('development')
                                            <span class="badge bg-warning">In Development</span>
                                            @break
                                        @case('maintenance')
                                            <span class="badge bg-info">Maintenance</span>
                                            @break
                                    @endswitch
                                </div>
                            </div>
                            <div class="text-end">
                                <div class="rating mb-2">
                                    @if($system->total_ratings > 0)
                                        <div class="d-flex align-items-center justify-content-end">
                                            <div class="stars me-2">
                                                @for($i = 1; $i <= 5; $i++)
                                                    @if($i <= floor($system->average_rating))
                                                        <i class="fas fa-star text-warning"></i>
                                                    @elseif($i - 0.5 <= $system->average_rating)
                                                        <i class="fas fa-star-half-alt text-warning"></i>
                                                    @else
                                                        <i class="far fa-star text-muted"></i>
                                                    @endif
                                                @endfor
                                            </div>
                                            <span class="fw-bold">{{ number_format($system->average_rating, 1) }}</span>
                                            <small class="text-muted ms-1">({{ $system->total_ratings }} reviews)</small>
                                        </div>
                                    @else
                                        <small class="text-muted">No ratings yet</small>
                                    @endif
                                </div>
                                <small class="text-muted">{{ number_format($system->views_count) }} views</small>
                            </div>
                        </div>

                        <p class="lead">{{ $system->description }}</p>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2 flex-wrap">
                            @if($system->system_url)
                                <a href="{{ $system->system_url }}" target="_blank" class="btn btn-primary">
                                    <i class="ri-external-link-line me-1"></i>Live Demo
                                </a>
                            @endif
                            @if($system->documentation_url)
                                <a href="{{ $system->documentation_url }}" target="_blank" class="btn btn-outline-info">
                                    <i class="ri-file-text-line me-1"></i>Documentation
                                </a>
                            @endif
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#ratingModal">
                                <i class="ri-star-line me-1"></i>Rate This System
                            </button>
                        </div>
                    </div>
                </div>

                <!-- System Image/Gallery -->
                @if($system->system_image_url || ($system->gallery_images && count($system->gallery_images) > 0))
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Screenshots</h5>
                        <div class="row g-3">
                            @if($system->system_image_url)
                                <div class="col-12">
                                    <img src="{{ $system->system_image_url }}" alt="{{ $system->title }}" 
                                         class="img-fluid rounded shadow-sm" style="max-height: 400px; width: 100%; object-fit: cover;">
                                </div>
                            @endif
                            @if($system->gallery_image_urls && count($system->gallery_image_urls) > 0)
                                @foreach($system->gallery_image_urls as $image)
                                    <div class="col-md-6">
                                        <img src="{{ $image }}" alt="{{ $system->title }}" 
                                             class="img-fluid rounded shadow-sm" style="height: 200px; width: 100%; object-fit: cover;">
                                    </div>
                                @endforeach
                            @endif
                        </div>
                    </div>
                </div>
                @endif

                <!-- Full Description -->
                @if($system->full_description)
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">About This System</h5>
                        <div class="system-description">
                            {!! nl2br(e($system->full_description)) !!}
                        </div>
                    </div>
                </div>
                @endif

                <!-- Technologies Used -->
                @if($system->technologies && count($system->technologies) > 0)
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Technologies Used</h5>
                        <div class="d-flex flex-wrap gap-2">
                            @foreach($system->technologies as $tech)
                                <span class="badge bg-primary-subtle text-primary fs-6 px-3 py-2">{{ $tech }}</span>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif

                <!-- Key Features -->
                @if($system->features && count($system->features) > 0)
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Key Features</h5>
                        <div class="row">
                            @foreach($system->features as $feature)
                                <div class="col-md-6 mb-2">
                                    <div class="d-flex align-items-start">
                                        <i class="ri-check-line text-success me-2 mt-1"></i>
                                        <span>{{ $feature }}</span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif

                <!-- Recent Reviews -->
                @if($ratings->count() > 0)
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Recent Reviews</h5>
                        @foreach($ratings->take(5) as $rating)
                            <div class="border-bottom pb-3 mb-3">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="mb-1">{{ $rating->display_name }}</h6>
                                        <div class="stars">
                                            @for($i = 1; $i <= 5; $i++)
                                                @if($i <= $rating->rating)
                                                    <i class="fas fa-star text-warning"></i>
                                                @else
                                                    <i class="far fa-star text-muted"></i>
                                                @endif
                                            @endfor
                                        </div>
                                    </div>
                                    <small class="text-muted">{{ $rating->created_at->diffForHumans() }}</small>
                                </div>
                                @if($rating->review)
                                    <p class="mb-0 text-muted">{{ $rating->review }}</p>
                                @endif
                            </div>
                        @endforeach
                        
                        @if($ratings->count() > 5)
                            <div class="text-center">
                                <button class="btn btn-outline-primary btn-sm" onclick="loadMoreReviews()">
                                    Load More Reviews
                                </button>
                            </div>
                        @endif
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- System Info -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">System Information</h5>
                        <table class="table table-sm">
                            @if($system->launch_date)
                            <tr>
                                <td><strong>Launch Date:</strong></td>
                                <td>{{ $system->launch_date->format('M d, Y') }}</td>
                            </tr>
                            @endif
                            @if($system->version)
                            <tr>
                                <td><strong>Version:</strong></td>
                                <td>{{ $system->version }}</td>
                            </tr>
                            @endif
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    @switch($system->status)
                                        @case('active')
                                            <span class="badge bg-success">Live & Active</span>
                                            @break
                                        @case('development')
                                            <span class="badge bg-warning">In Development</span>
                                            @break
                                        @case('maintenance')
                                            <span class="badge bg-info">Under Maintenance</span>
                                            @break
                                    @endswitch
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Total Views:</strong></td>
                                <td>{{ number_format($system->views_count) }}</td>
                            </tr>
                            <tr>
                                <td><strong>Total Ratings:</strong></td>
                                <td>{{ $system->total_ratings }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Rating Distribution -->
                @if($system->total_ratings > 0)
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Rating Distribution</h5>
                        @foreach(range(5, 1) as $star)
                            @php
                                $count = $ratingDistribution[$star] ?? 0;
                                $percentage = $system->total_ratings > 0 ? ($count / $system->total_ratings) * 100 : 0;
                            @endphp
                            <div class="d-flex align-items-center mb-2">
                                <span class="me-2">{{ $star }}</span>
                                <i class="fas fa-star text-warning me-2"></i>
                                <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                    <div class="progress-bar bg-warning" style="width: {{ $percentage }}%"></div>
                                </div>
                                <small class="text-muted">{{ $count }}</small>
                            </div>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Featured Reviews -->
                @if($featuredRatings->count() > 0)
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Featured Reviews</h5>
                        @foreach($featuredRatings as $rating)
                            <div class="border-bottom pb-2 mb-2">
                                <div class="d-flex justify-content-between align-items-start mb-1">
                                    <h6 class="mb-0 small">{{ $rating->display_name }}</h6>
                                    <div class="stars">
                                        @for($i = 1; $i <= 5; $i++)
                                            @if($i <= $rating->rating)
                                                <i class="fas fa-star text-warning small"></i>
                                            @else
                                                <i class="far fa-star text-muted small"></i>
                                            @endif
                                        @endfor
                                    </div>
                                </div>
                                @if($rating->review)
                                    <p class="mb-0 small text-muted">{{ Str::limit($rating->review, 100) }}</p>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
<!-- Rating Modal -->
<div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ratingModalLabel">Rate {{ $system->title }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="ratingForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Your Rating</label>
                        <div class="rating-input d-flex gap-1 mb-2">
                            @for($i = 1; $i <= 5; $i++)
                                <i class="far fa-star rating-star" data-rating="{{ $i }}" style="font-size: 1.5rem; cursor: pointer; color: #ddd;"></i>
                            @endfor
                        </div>
                        <input type="hidden" id="rating" name="rating" required>
                        <div class="invalid-feedback" id="rating-error"></div>
                    </div>

                    <div class="mb-3">
                        <label for="review" class="form-label">Your Review (Optional)</label>
                        <textarea class="form-control" id="review" name="review" rows="4"
                                  placeholder="Share your experience with this system..."></textarea>
                    </div>

                    @guest
                    <div class="mb-3">
                        <label for="user_name" class="form-label">Your Name</label>
                        <input type="text" class="form-control" id="user_name" name="user_name" required>
                        <div class="invalid-feedback" id="user_name-error"></div>
                    </div>

                    <div class="mb-3">
                        <label for="user_email" class="form-label">Your Email</label>
                        <input type="email" class="form-control" id="user_email" name="user_email" required>
                        <div class="invalid-feedback" id="user_email-error"></div>
                    </div>
                    @endguest
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Submit Rating</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@push('styles')
<style>
    .rating-star:hover,
    .rating-star.active {
        color: #ffc107 !important;
    }

    .system-description {
        line-height: 1.6;
    }

    .stars i {
        font-size: 0.9rem;
    }

    .progress {
        background-color: #f8f9fa;
    }

    .badge {
        font-weight: 500;
    }

    .card {
        border: 1px solid #e9ecef;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transition: box-shadow 0.3s ease;
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Rating stars functionality
    const ratingStars = document.querySelectorAll('.rating-star');
    const ratingInput = document.getElementById('rating');
    let selectedRating = 0;

    ratingStars.forEach((star, index) => {
        star.addEventListener('mouseover', function() {
            highlightStars(index + 1);
        });

        star.addEventListener('mouseout', function() {
            highlightStars(selectedRating);
        });

        star.addEventListener('click', function() {
            selectedRating = index + 1;
            ratingInput.value = selectedRating;
            highlightStars(selectedRating);
            clearError('rating');
        });
    });

    function highlightStars(rating) {
        ratingStars.forEach((star, index) => {
            if (index < rating) {
                star.classList.remove('far');
                star.classList.add('fas', 'active');
            } else {
                star.classList.remove('fas', 'active');
                star.classList.add('far');
            }
        });
    }

    // Form submission
    document.getElementById('ratingForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        // Clear previous errors
        clearAllErrors();

        // Validate rating
        if (!ratingInput.value) {
            showError('rating', 'Please select a rating');
            return;
        }

        // Disable submit button
        submitBtn.disabled = true;
        submitBtn.textContent = 'Submitting...';

        // Submit rating
        fetch('{{ route("system.rate", $system->slug) }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showToast('Rating submitted successfully!', 'success');

                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('ratingModal'));
                modal.hide();

                // Reset form
                this.reset();
                selectedRating = 0;
                highlightStars(0);

                // Optionally reload page to show new rating
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                if (data.errors) {
                    Object.keys(data.errors).forEach(field => {
                        showError(field, data.errors[field][0]);
                    });
                } else {
                    showToast(data.message || 'Failed to submit rating', 'error');
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Failed to submit rating. Please try again.', 'error');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    });

    function showError(field, message) {
        const input = document.getElementById(field);
        const errorDiv = document.getElementById(field + '-error');

        if (input) {
            input.classList.add('is-invalid');
        }
        if (errorDiv) {
            errorDiv.textContent = message;
        }
    }

    function clearError(field) {
        const input = document.getElementById(field);
        const errorDiv = document.getElementById(field + '-error');

        if (input) {
            input.classList.remove('is-invalid');
        }
        if (errorDiv) {
            errorDiv.textContent = '';
        }
    }

    function clearAllErrors() {
        document.querySelectorAll('.is-invalid').forEach(el => {
            el.classList.remove('is-invalid');
        });
        document.querySelectorAll('.invalid-feedback').forEach(el => {
            el.textContent = '';
        });
    }

    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 3000);
    }
});

// Load more reviews function
function loadMoreReviews() {
    // This would typically load more reviews via AJAX
    // For now, we'll just show a message
    alert('Load more reviews functionality would be implemented here');
}
</script>
@endpush
