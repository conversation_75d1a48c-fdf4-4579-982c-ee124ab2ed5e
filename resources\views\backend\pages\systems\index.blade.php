@extends('backend.layouts.app')

@section('title', 'Systems Management')

@section('content')
<!-- Page Title -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Systems Management</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Systems</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="card-title mb-0">
                    <i class="ri-computer-line me-2"></i>Systems Management
                </h4>
                <div>
                    <a href="{{ route('systems.create') }}" class="btn btn-primary">
                        <i class="ri-add-line me-1"></i>Add New System
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if($systems->count() > 0)
                    <!-- Systems Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card border-0 bg-primary bg-opacity-10">
                                <div class="card-body text-center">
                                    <h3 class="text-primary mb-1">{{ $systems->count() }}</h3>
                                    <p class="text-muted mb-0">Total Systems</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 bg-success bg-opacity-10">
                                <div class="card-body text-center">
                                    <h3 class="text-success mb-1">{{ $systems->where('status', 'active')->count() }}</h3>
                                    <p class="text-muted mb-0">Active Systems</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 bg-warning bg-opacity-10">
                                <div class="card-body text-center">
                                    <h3 class="text-warning mb-1">{{ $systems->where('featured', true)->count() }}</h3>
                                    <p class="text-muted mb-0">Featured Systems</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 bg-info bg-opacity-10">
                                <div class="card-body text-center">
                                    <h3 class="text-info mb-1">{{ number_format($systems->sum('total_ratings')) }}</h3>
                                    <p class="text-muted mb-0">Total Ratings</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Drag and Drop Notice -->
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <i class="ri-information-line me-2"></i>
                        <strong>Tip:</strong> You can drag and drop rows to reorder systems. Changes are saved automatically.
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover table-striped" id="systemsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th width="30"><i class="ri-drag-move-line"></i></th>
                                    <th>#</th>
                                    <th>System</th>
                                    <th>Category</th>
                                    <th>Rating</th>
                                    <th>Status</th>
                                    <th>Featured</th>
                                    <th>Views</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="sortable-systems">
                                @foreach($systems as $system)
                                <tr data-id="{{ $system->id }}" data-sort-order="{{ $system->sort_order }}">
                                    <td class="drag-handle" style="cursor: move;">
                                        <i class="ri-drag-move-2-line text-muted"></i>
                                    </td>
                                    <td>{{ $system->sort_order }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($system->system_image_url)
                                                <img src="{{ $system->system_image_url }}" alt="{{ $system->title }}" 
                                                     class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                            @else
                                                <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 50px; height: 50px;">
                                                    <i class="ri-computer-line text-muted"></i>
                                                </div>
                                            @endif
                                            <div>
                                                <h6 class="mb-1">{{ $system->title }}</h6>
                                                <p class="text-muted mb-0 small">{{ Str::limit($system->description, 50) }}</p>
                                                @if($system->technologies)
                                                    <div class="mt-1">
                                                        @foreach(array_slice($system->technologies, 0, 3) as $tech)
                                                            <span class="badge bg-secondary-subtle text-secondary me-1">{{ $tech }}</span>
                                                        @endforeach
                                                        @if(count($system->technologies) > 3)
                                                            <span class="badge bg-light text-muted">+{{ count($system->technologies) - 3 }} more</span>
                                                        @endif
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        @if($system->category)
                                            <span class="badge bg-primary-subtle text-primary">{{ $system->category }}</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-2">
                                                @for($i = 1; $i <= 5; $i++)
                                                    @if($i <= floor($system->average_rating))
                                                        <i class="fas fa-star text-warning"></i>
                                                    @elseif($i - 0.5 <= $system->average_rating)
                                                        <i class="fas fa-star-half-alt text-warning"></i>
                                                    @else
                                                        <i class="far fa-star text-muted"></i>
                                                    @endif
                                                @endfor
                                            </div>
                                            <small class="text-muted">
                                                {{ number_format($system->average_rating, 1) }} ({{ $system->total_ratings }})
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input status-toggle" type="checkbox"
                                                   data-id="{{ $system->id }}" {{ $system->status === 'active' ? 'checked' : '' }}>
                                            <label class="form-check-label">
                                                <span class="status-text">{{ ucfirst($system->status) }}</span>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input featured-toggle" type="checkbox" 
                                                   data-id="{{ $system->id }}" {{ $system->featured ? 'checked' : '' }}>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info-subtle text-info">{{ number_format($system->views_count) }}</span>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ $system->created_at->format('M d, Y') }}</small>
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                                    data-bs-toggle="dropdown">
                                                <i class="ri-more-2-line"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('systems.show', $system) }}">
                                                        <i class="ri-eye-line me-2"></i>View Details
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('systems.edit', $system) }}">
                                                        <i class="ri-edit-line me-2"></i>Edit
                                                    </a>
                                                </li>
                                                @if($system->system_url)
                                                <li>
                                                    <a class="dropdown-item" href="{{ $system->system_url }}" target="_blank">
                                                        <i class="ri-external-link-line me-2"></i>View Live
                                                    </a>
                                                </li>
                                                @endif
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <form action="{{ route('systems.duplicate', $system) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        <button type="submit" class="dropdown-item">
                                                            <i class="ri-file-copy-line me-2"></i>Duplicate
                                                        </button>
                                                    </form>
                                                </li>
                                                <li>
                                                    <form action="{{ route('systems.destroy', $system) }}" method="POST"
                                                          class="d-inline delete-form">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="dropdown-item text-danger">
                                                            <i class="ri-delete-bin-line me-2"></i>Delete
                                                        </button>
                                                    </form>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-5">
                        <div class="mb-4">
                            <i class="ri-computer-line display-4 text-muted"></i>
                        </div>
                        <h5 class="text-muted">No Systems Found</h5>
                        <p class="text-muted mb-4">Start by creating your first system to showcase your work.</p>
                        <a href="{{ route('systems.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Create First System
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .drag-handle {
        cursor: move;
    }
    .sortable-ghost {
        opacity: 0.5;
    }
    .sortable-chosen {
        background-color: #f8f9fa;
    }
    .table tbody tr:hover {
        background-color: #f8f9fa;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize sortable
    const sortableElement = document.getElementById('sortable-systems');
    if (sortableElement) {
        const sortable = Sortable.create(sortableElement, {
            handle: '.drag-handle',
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            onEnd: function(evt) {
                updateSortOrder();
            }
        });
    }

    // Update sort order function
    function updateSortOrder() {
        const rows = document.querySelectorAll('#sortable-systems tr');
        const systems = [];

        rows.forEach((row, index) => {
            const id = row.getAttribute('data-id');
            if (id) {
                systems.push({
                    id: parseInt(id),
                    sort_order: index + 1
                });
                // Update the display order number
                const orderCell = row.querySelector('td:nth-child(2)');
                if (orderCell) {
                    orderCell.textContent = index + 1;
                }
            }
        });

        // Send AJAX request to update sort order
        fetch('{{ route("systems.update-sort-order") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ systems: systems })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showToast('Sort order updated successfully!', 'success');
            } else {
                showToast('Failed to update sort order', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Failed to update sort order', 'error');
        });
    }

    // Status toggle functionality
    document.querySelectorAll('.status-toggle').forEach(toggle => {
        toggle.addEventListener('change', function() {
            const systemId = this.getAttribute('data-id');
            const isChecked = this.checked;
            const statusText = this.parentElement.querySelector('.status-text');

            fetch(`/admin/systems/${systemId}/toggle-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    statusText.textContent = data.status.charAt(0).toUpperCase() + data.status.slice(1);
                    showToast(data.message, 'success');
                } else {
                    // Revert the toggle if failed
                    this.checked = !isChecked;
                    showToast(data.message || 'Failed to update status', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // Revert the toggle if failed
                this.checked = !isChecked;
                showToast('Failed to update status', 'error');
            });
        });
    });

    // Featured toggle functionality
    document.querySelectorAll('.featured-toggle').forEach(toggle => {
        toggle.addEventListener('change', function() {
            const systemId = this.getAttribute('data-id');
            const isChecked = this.checked;

            fetch(`/admin/systems/${systemId}/toggle-featured`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                } else {
                    // Revert the toggle if failed
                    this.checked = !isChecked;
                    showToast(data.message || 'Failed to update featured status', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // Revert the toggle if failed
                this.checked = !isChecked;
                showToast('Failed to update featured status', 'error');
            });
        });
    });

    // Delete confirmation
    document.querySelectorAll('.delete-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            if (confirm('Are you sure you want to delete this system? This action cannot be undone.')) {
                this.submit();
            }
        });
    });

    // Toast notification function
    function showToast(message, type = 'info') {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(toast);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 3000);
    }
});
</script>
@endpush
