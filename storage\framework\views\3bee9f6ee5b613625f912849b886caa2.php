<?php $__env->startSection('title', 'Blog Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Blog Management</h1>
        <a href="<?php echo e(route('admin.blog.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Create Post
        </a>
    </div>

    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.blog.index')); ?>" class="row g-3">
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">All Status</option>
                        <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($status); ?>" <?php echo e(request('status') === $status ? 'selected' : ''); ?>>
                                <?php echo e(ucfirst($status)); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-7">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" name="search" id="search" class="form-control" 
                           placeholder="Search by title, excerpt, or content..."
                           value="<?php echo e(request('search')); ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search me-2"></i>Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Blog Posts Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">Blog Posts (<?php echo e($posts->total()); ?>)</h6>
                <?php if($posts->count() > 0): ?>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="toggleBulkActions()">
                        <i class="fas fa-tasks me-2"></i>Bulk Actions
                    </button>
                <?php endif; ?>
            </div>
        </div>
        <div class="card-body">
            <?php if($posts->count() > 0): ?>
                <form id="bulkForm" action="<?php echo e(route('admin.blog.bulk')); ?>" method="POST" style="display: none;">
                    <?php echo csrf_field(); ?>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <select name="action" class="form-select" required>
                                <option value="">Select Action</option>
                                <option value="publish">Publish</option>
                                <option value="draft">Move to Draft</option>
                                <option value="archive">Archive</option>
                                <option value="delete">Delete</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-primary me-2">Apply</button>
                            <button type="button" class="btn btn-secondary" onclick="toggleBulkActions()">Cancel</button>
                        </div>
                    </div>
                </form>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th id="bulkCheckboxHeader" style="display: none;">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>Post</th>
                                <th>Status</th>
                                <th>SEO</th>
                                <th>Stats</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td class="bulkCheckbox" style="display: none;">
                                    <input type="checkbox" name="posts[]" value="<?php echo e($post->id); ?>" 
                                           form="bulkForm" class="form-check-input post-checkbox">
                                </td>
                                <td>
                                    <div class="d-flex align-items-start">
                                        <?php if($post->featured_image): ?>
                                            <img src="<?php echo e(Storage::url($post->featured_image)); ?>" 
                                                 alt="<?php echo e($post->title); ?>" 
                                                 class="me-3 rounded" 
                                                 style="width: 60px; height: 40px; object-fit: cover;">
                                        <?php endif; ?>
                                        <div>
                                            <strong><?php echo e(Str::limit($post->title, 40)); ?></strong>
                                            <?php if($post->featured): ?>
                                                <span class="badge bg-warning text-dark ms-2">Featured</span>
                                            <?php endif; ?>
                                            <br>
                                            <small class="text-muted"><?php echo e(Str::limit($post->excerpt, 60)); ?></small>
                                            <br>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i><?php echo e($post->reading_time); ?> min read
                                            </small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if($post->status === 'published'): ?>
                                        <span class="badge bg-success">Published</span>
                                    <?php elseif($post->status === 'draft'): ?>
                                        <span class="badge bg-warning">Draft</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Archived</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="small">
                                        <?php if($post->meta_title): ?>
                                            <i class="fas fa-check text-success" title="Has Meta Title"></i>
                                        <?php else: ?>
                                            <i class="fas fa-times text-danger" title="No Meta Title"></i>
                                        <?php endif; ?>
                                        
                                        <?php if($post->meta_description): ?>
                                            <i class="fas fa-check text-success" title="Has Meta Description"></i>
                                        <?php else: ?>
                                            <i class="fas fa-times text-danger" title="No Meta Description"></i>
                                        <?php endif; ?>
                                        
                                        <?php if($post->og_title || $post->og_description): ?>
                                            <i class="fab fa-facebook text-primary" title="Has OG Tags"></i>
                                        <?php endif; ?>
                                        
                                        <?php if($post->twitter_title || $post->twitter_description): ?>
                                            <i class="fab fa-twitter text-info" title="Has Twitter Cards"></i>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <small>
                                        <i class="fas fa-eye me-1"></i><?php echo e($post->views_count); ?><br>
                                        <?php if($post->categories): ?>
                                            <span class="badge bg-light text-dark"><?php echo e(count($post->categories)); ?> categories</span>
                                        <?php endif; ?>
                                    </small>
                                </td>
                                <td>
                                    <small>
                                        <?php if($post->published_at): ?>
                                            <strong>Published:</strong><br>
                                            <?php echo e($post->published_at->format('M j, Y')); ?>

                                        <?php else: ?>
                                            <strong>Created:</strong><br>
                                            <?php echo e($post->created_at->format('M j, Y')); ?>

                                        <?php endif; ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?php echo e(route('admin.blog.edit', $post->id)); ?>" 
                                           class="btn btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.blog.seo', $post->id)); ?>" 
                                           class="btn btn-outline-info" title="SEO">
                                            <i class="fas fa-search"></i>
                                        </a>
                                        <?php if($post->status === 'published'): ?>
                                            <a href="<?php echo e(route('blog.show', $post->slug)); ?>" 
                                               class="btn btn-outline-success" title="View" target="_blank">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deletePost(<?php echo e($post->id); ?>)" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        Showing <?php echo e($posts->firstItem()); ?> to <?php echo e($posts->lastItem()); ?> of <?php echo e($posts->total()); ?> results
                    </div>
                    <?php echo e($posts->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-blog fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No blog posts found</h5>
                    <p class="text-muted">Start creating engaging content for your audience.</p>
                    <a href="<?php echo e(route('admin.blog.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Your First Post
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this blog post? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function toggleBulkActions() {
    const bulkForm = document.getElementById('bulkForm');
    const bulkCheckboxes = document.querySelectorAll('.bulkCheckbox');
    const bulkCheckboxHeader = document.getElementById('bulkCheckboxHeader');
    
    if (bulkForm.style.display === 'none') {
        bulkForm.style.display = 'block';
        bulkCheckboxHeader.style.display = 'table-cell';
        bulkCheckboxes.forEach(cb => cb.style.display = 'table-cell');
    } else {
        bulkForm.style.display = 'none';
        bulkCheckboxHeader.style.display = 'none';
        bulkCheckboxes.forEach(cb => cb.style.display = 'none');
        document.getElementById('selectAll').checked = false;
        document.querySelectorAll('.post-checkbox').forEach(cb => cb.checked = false);
    }
}

document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.post-checkbox');
    checkboxes.forEach(cb => cb.checked = this.checked);
});

function deletePost(id) {
    const form = document.getElementById('deleteForm');
    form.action = `/admin/blog/${id}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\Grandtek\resources\views/admin/blog/index.blade.php ENDPATH**/ ?>