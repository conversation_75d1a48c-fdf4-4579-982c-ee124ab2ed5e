<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\ProjectRating;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ProjectController extends Controller
{
    /**
     * Display a listing of projects
     */
    public function index()
    {
        try {
            $projects = Project::ordered()->get();
            return view('backend.pages.projects.index', compact('projects'));
        } catch (\Exception $e) {
            Log::error('Error loading projects: ' . $e->getMessage());
            return view('backend.pages.projects.index', ['projects' => collect()]);
        }
    }

    /**
     * Show the form for creating a new project
     */
    public function create()
    {
        return view('backend.pages.projects.create');
    }

    /**
     * Store a newly created project
     */
    public function store(Request $request)
    {
        // Debug: Log the incoming request
        Log::info('Project creation attempt', [
            'request_data' => $request->all(),
            'has_title' => $request->has('title'),
            'has_description' => $request->has('description'),
            'has_status' => $request->has('status'),
        ]);

        // Handle technologies JSON string
        $technologies = null;
        if ($request->has('technologies') && !empty($request->technologies)) {
            $technologies = json_decode($request->technologies, true);
            if (!is_array($technologies)) {
                $technologies = null;
            }
        }

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:projects,slug',
            'description' => 'required|string|max:500',
            'short_description' => 'nullable|string|max:300',
            'full_description' => 'nullable|string',
            'category' => 'nullable|string|max:100',
            'client_name' => 'nullable|string|max:255',
            'project_url' => 'nullable|url|max:255',
            'technologies' => 'nullable|string', // Accept as JSON string
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'project_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'status' => 'required|in:active,inactive,completed,in_progress',
            'featured' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            Log::error('Project validation failed', [
                'errors' => $validator->errors()->toArray(),
                'input' => $request->all()
            ]);
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->only([
            'title', 'slug', 'description', 'short_description', 'full_description',
            'category', 'client_name', 'project_url', 'start_date',
            'end_date', 'status', 'featured', 'meta_title', 'meta_description', 'meta_keywords'
        ]);

        // Set technologies from parsed JSON
        $data['technologies'] = $technologies;

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['title']);
        }

        // Handle main project image upload
        if ($request->hasFile('project_image')) {
            $data['project_image'] = $request->file('project_image')->store('projects/images', 'public');
        }

        // Handle gallery images upload
        if ($request->hasFile('gallery_images')) {
            $galleryImages = [];
            foreach ($request->file('gallery_images') as $image) {
                $galleryImages[] = $image->store('projects/gallery', 'public');
            }
            $data['gallery_images'] = $galleryImages;
        }

        // Set featured flag
        $data['featured'] = $request->has('featured');

        try {
            Log::info('Creating project with data', ['data' => $data]);
            $project = Project::create($data);
            Log::info('Project created successfully', ['project_id' => $project->id]);

            return redirect()->route('projects.index')
                ->with('success', 'Project created successfully!');
        } catch (\Exception $e) {
            Log::error('Error creating project', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            return redirect()->back()
                ->withErrors(['error' => 'Failed to create project: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Display the specified project
     */
    public function show(Project $project)
    {
        return view('backend.pages.projects.show', compact('project'));
    }

    /**
     * Show the form for editing the specified project
     */
    public function edit(Project $project)
    {
        return view('backend.pages.projects.edit', compact('project'));
    }

    /**
     * Update the specified project
     */
    public function update(Request $request, Project $project)
    {
        // Handle technologies JSON string
        $technologies = null;
        if ($request->has('technologies') && !empty($request->technologies)) {
            $technologies = json_decode($request->technologies, true);
            if (!is_array($technologies)) {
                $technologies = null;
            }
        }

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:projects,slug,' . $project->id,
            'description' => 'required|string|max:500',
            'short_description' => 'nullable|string|max:300',
            'full_description' => 'nullable|string',
            'category' => 'nullable|string|max:100',
            'client_name' => 'nullable|string|max:255',
            'project_url' => 'nullable|url|max:255',
            'technologies' => 'nullable|string', // Accept as JSON string
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'project_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'status' => 'required|in:active,inactive,completed,in_progress',
            'featured' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->only([
            'title', 'slug', 'description', 'short_description', 'full_description',
            'category', 'client_name', 'project_url', 'start_date',
            'end_date', 'status', 'featured', 'meta_title', 'meta_description', 'meta_keywords'
        ]);

        // Set technologies from parsed JSON
        $data['technologies'] = $technologies;

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['title']);
        }

        // Handle main project image upload
        if ($request->hasFile('project_image')) {
            // Delete old image
            if ($project->project_image && Storage::disk('public')->exists($project->project_image)) {
                Storage::disk('public')->delete($project->project_image);
            }
            $data['project_image'] = $request->file('project_image')->store('projects/images', 'public');
        }

        // Handle gallery images upload
        if ($request->hasFile('gallery_images')) {
            // Keep existing gallery images and add new ones
            $existingImages = $project->gallery_images ?? [];
            $newImages = [];
            foreach ($request->file('gallery_images') as $image) {
                $newImages[] = $image->store('projects/gallery', 'public');
            }
            $data['gallery_images'] = array_merge($existingImages, $newImages);
        }

        // Set featured flag
        $data['featured'] = $request->has('featured');

        $project->update($data);

        return redirect()->route('projects.index')
            ->with('success', 'Project updated successfully!');
    }

    /**
     * Remove the specified project
     */
    public function destroy(Project $project)
    {
        try {
            $project->delete();
            return redirect()->route('projects.index')
                ->with('success', 'Project deleted successfully!');
        } catch (\Exception $e) {
            Log::error('Error deleting project: ' . $e->getMessage());
            return redirect()->route('projects.index')
                ->with('error', 'Failed to delete project');
        }
    }

    /**
     * Toggle project status
     */
    public function toggleStatus(Project $project)
    {
        try {
            $newStatus = $project->status === 'active' ? 'inactive' : 'active';
            $project->update(['status' => $newStatus]);

            return response()->json([
                'success' => true,
                'status' => $newStatus,
                'message' => 'Project status updated successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error('Error toggling project status: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update project status'
            ], 500);
        }
    }

    /**
     * Toggle featured status
     */
    public function toggleFeatured(Project $project)
    {
        try {
            $project->update(['featured' => !$project->featured]);

            return response()->json([
                'success' => true,
                'featured' => $project->featured,
                'message' => 'Project featured status updated successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error('Error toggling project featured status: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update featured status'
            ], 500);
        }
    }

    /**
     * Reorder projects
     */
    public function reorder(Request $request)
    {
        try {
            $order = $request->input('order', []);

            if (empty($order)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No order data provided'
                ], 400);
            }

            foreach ($order as $index => $id) {
                Project::where('id', $id)->update(['sort_order' => $index + 1]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Projects reordered successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error('Error reordering projects: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to reorder projects'
            ], 500);
        }
    }

    /**
     * Duplicate a project
     */
    public function duplicate(Project $project)
    {
        try {
            $newProject = $project->replicate();
            $newProject->title = $project->title . ' (Copy)';
            $newProject->slug = null; // Will be auto-generated
            $newProject->status = 'inactive'; // Set copy as inactive by default
            $newProject->sort_order = Project::max('sort_order') + 1;
            $newProject->save();

            return redirect()->route('projects.index')
                ->with('success', 'Project duplicated successfully!');
        } catch (\Exception $e) {
            Log::error('Error duplicating project: ' . $e->getMessage());
            return redirect()->route('projects.index')
                ->with('error', 'Failed to duplicate project');
        }
    }

    /**
     * Remove gallery image
     */
    public function removeGalleryImage(Project $project, $image_index)
    {
        try {
            $galleryImages = $project->gallery_images ?? [];

            if (isset($galleryImages[$image_index])) {
                $imagePath = $galleryImages[$image_index];

                // Delete file from storage
                if (Storage::disk('public')->exists($imagePath)) {
                    Storage::disk('public')->delete($imagePath);
                }

                // Remove from array
                unset($galleryImages[$image_index]);
                $galleryImages = array_values($galleryImages); // Re-index array

                $project->update(['gallery_images' => $galleryImages]);

                return response()->json([
                    'success' => true,
                    'message' => 'Gallery image removed successfully!'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Image not found'
            ], 404);
        } catch (\Exception $e) {
            Log::error('Error removing gallery image: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove image'
            ], 500);
        }
    }

    /**
     * Get projects for homepage
     */
    public function getHomepageData()
    {
        // First try to get featured projects, if none exist, get all active projects
        $projects = Project::active()->featured()->ordered()->take(6)->get();

        if ($projects->isEmpty()) {
            // If no featured projects, get all active projects
            $projects = Project::active()->ordered()->take(6)->get();
        }

        if ($projects->isEmpty()) {
            return null;
        }

        return $projects->map(function ($project) {
            return [
                'id' => $project->id,
                'title' => $project->title,
                'slug' => $project->slug,
                'description' => $project->description,
                'short_description' => $project->short_description,
                'category' => $project->category,
                'project_image_url' => $project->project_image_url,
                'project_url' => $project->project_url,
                'technologies' => $project->technologies,
                'status' => $project->status,
            ];
        });
    }

    /**
     * Show project detail page (frontend)
     */
    public function showProject(Project $project)
    {
        // Only show active projects on frontend
        if ($project->status !== 'active') {
            abort(404);
        }

        // Increment views count
        $project->incrementViews();

        // Load ratings with pagination
        $ratings = $project->ratings()->active()->latest()->paginate(10);

        // Get rating distribution
        $ratingDistribution = $project->getRatingDistribution();

        return view('frontend.pages.project-detail', compact('project', 'ratings', 'ratingDistribution'));
    }

    /**
     * Store a rating for a project
     */
    public function storeRating(Request $request, Project $project)
    {
        $validator = Validator::make($request->all(), [
            'rating' => 'required|integer|min:1|max:5',
            'review' => 'nullable|string|max:1000',
            'user_name' => 'required_without:user_id|string|max:255',
            'user_email' => 'required_without:user_id|email|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $data = [
            'project_id' => $project->id,
            'rating' => $request->rating,
            'review' => $request->review,
            'ip_address' => $request->ip(),
            'status' => 'active',
        ];

        // Check if user is authenticated
        if (Auth::check()) {
            $data['user_id'] = Auth::id();

            // Check if user already rated this project
            $existingRating = ProjectRating::where('project_id', $project->id)
                ->where('user_id', Auth::id())
                ->first();

            if ($existingRating) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already rated this project.'
                ], 422);
            }
        } else {
            // For anonymous users
            $data['user_name'] = $request->user_name;
            $data['user_email'] = $request->user_email;

            // Check for duplicate anonymous ratings from same IP/email
            $existingRating = ProjectRating::where('project_id', $project->id)
                ->where(function($query) use ($request) {
                    $query->where('ip_address', $request->ip())
                          ->orWhere('user_email', $request->user_email);
                })
                ->whereNull('user_id')
                ->first();

            if ($existingRating) {
                return response()->json([
                    'success' => false,
                    'message' => 'A rating from this email or location already exists for this project.'
                ], 422);
            }
        }

        try {
            $rating = ProjectRating::create($data);

            return response()->json([
                'success' => true,
                'message' => 'Rating submitted successfully!',
                'rating' => [
                    'id' => $rating->id,
                    'rating' => $rating->rating,
                    'review' => $rating->review,
                    'display_name' => $rating->display_name,
                    'created_at' => $rating->created_at->format('M d, Y'),
                ],
                'project' => [
                    'average_rating' => $project->fresh()->average_rating,
                    'total_ratings' => $project->fresh()->total_ratings,
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating project rating', [
                'error' => $e->getMessage(),
                'project_id' => $project->id,
                'data' => $data
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to submit rating. Please try again.'
            ], 500);
        }
    }

    /**
     * Show all projects page (frontend)
     */
    public function allProjects()
    {
        $projects = Project::active()->ordered()->paginate(12);
        return view('frontend.pages.projects', compact('projects'));
    }

    /**
     * Show project ratings for admin
     */
    public function showRatings(Project $project)
    {
        $ratings = $project->ratings()->with('user')->latest()->paginate(20);
        $ratingDistribution = $project->getRatingDistribution();

        return view('backend.pages.projects.ratings', compact('project', 'ratings', 'ratingDistribution'));
    }

    /**
     * Toggle rating featured status
     */
    public function toggleRatingFeatured(Project $project, ProjectRating $rating)
    {
        try {
            $rating->update(['is_featured' => !$rating->is_featured]);

            return response()->json([
                'success' => true,
                'is_featured' => $rating->is_featured,
                'message' => 'Rating featured status updated successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error('Error toggling rating featured status: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update featured status'
            ], 500);
        }
    }

    /**
     * Toggle rating verified status
     */
    public function toggleRatingVerified(Project $project, ProjectRating $rating)
    {
        try {
            $rating->update(['is_verified' => !$rating->is_verified]);

            return response()->json([
                'success' => true,
                'is_verified' => $rating->is_verified,
                'message' => 'Rating verified status updated successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error('Error toggling rating verified status: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update verified status'
            ], 500);
        }
    }

    /**
     * Toggle rating status
     */
    public function toggleRatingStatus(Project $project, ProjectRating $rating)
    {
        try {
            $newStatus = $rating->status === 'active' ? 'inactive' : 'active';
            $rating->update(['status' => $newStatus]);

            return response()->json([
                'success' => true,
                'status' => $newStatus,
                'message' => 'Rating status updated successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error('Error toggling rating status: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update rating status'
            ], 500);
        }
    }

    /**
     * Delete a project rating
     */
    public function deleteRating(Project $project, ProjectRating $rating)
    {
        try {
            $rating->delete();

            return response()->json([
                'success' => true,
                'message' => 'Rating deleted successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error('Error deleting rating: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete rating'
            ], 500);
        }
    }
}
