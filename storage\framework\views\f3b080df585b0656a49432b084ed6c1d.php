<?php $__env->startSection('title', isset($post->id) ? 'Edit Blog Post' : 'Create Blog Post'); ?>

<?php $__env->startPush('styles'); ?>
<style>
.character-count {
    font-size: 0.875rem;
    color: #6c757d;
}
.character-count.warning {
    color: #ffc107;
}
.character-count.danger {
    color: #dc3545;
}
.tag-input {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.5rem;
    min-height: 38px;
}
.tag-item {
    display: inline-block;
    background: #007bff;
    color: white;
    padding: 0.25rem 0.5rem;
    margin: 0.125rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}
.tag-item .remove-tag {
    margin-left: 0.5rem;
    cursor: pointer;
    font-weight: bold;
}
.preview-image {
    max-width: 200px;
    max-height: 150px;
    object-fit: cover;
    border-radius: 0.375rem;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><?php echo e(isset($post->id) ? 'Edit Blog Post' : 'Create Blog Post'); ?></h1>
        <div class="btn-group">
            <a href="<?php echo e(route('admin.blog.index')); ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Posts
            </a>
        </div>
    </div>

    <form action="<?php echo e(isset($post->id) ? route('admin.blog.update', $post->id) : route('admin.blog.store')); ?>"
          method="POST" enctype="multipart/form-data" id="blogForm">
        <?php echo csrf_field(); ?>
        <?php if(isset($post->id)): ?>
            <?php echo method_field('PUT'); ?>
        <?php endif; ?>

        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Post Content</h6>
                    </div>
                    <div class="card-body">
                        <!-- Title -->
                        <div class="mb-3">
                            <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="title" name="title" value="<?php echo e(old('title', $post->title)); ?>"
                                   maxlength="255" required>
                            <div class="character-count mt-1">
                                <span id="title-count">0</span>/255 characters
                            </div>
                            <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Slug -->
                        <div class="mb-3">
                            <label for="slug" class="form-label">Slug</label>
                            <input type="text" class="form-control <?php $__errorArgs = ['slug'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="slug" name="slug" value="<?php echo e(old('slug', $post->slug)); ?>"
                                   maxlength="255">
                            <small class="form-text text-muted">Leave empty to auto-generate from title</small>
                            <?php $__errorArgs = ['slug'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Excerpt -->
                        <div class="mb-3">
                            <label for="excerpt" class="form-label">Excerpt</label>
                            <textarea class="form-control <?php $__errorArgs = ['excerpt'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                      id="excerpt" name="excerpt" rows="3"
                                      maxlength="500"><?php echo e(old('excerpt', $post->excerpt)); ?></textarea>
                            <div class="character-count mt-1">
                                <span id="excerpt-count">0</span>/500 characters
                            </div>
                            <?php $__errorArgs = ['excerpt'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Content -->
                        <div class="mb-3">
                            <label for="content" class="form-label">Content <span class="text-danger">*</span></label>
                            <textarea class="form-control <?php $__errorArgs = ['content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                      id="content" name="content" rows="15"
                                      required><?php echo e(old('content', $post->content)); ?></textarea>
                            <?php $__errorArgs = ['content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- SEO Section -->
                <div class="card shadow mt-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-success">SEO Settings</h6>
                    </div>
                    <div class="card-body">
                        <!-- Meta Title -->
                        <div class="mb-3">
                            <label for="meta_title" class="form-label">Meta Title</label>
                            <input type="text" class="form-control <?php $__errorArgs = ['meta_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="meta_title" name="meta_title" value="<?php echo e(old('meta_title', $post->meta_title)); ?>"
                                   maxlength="60">
                            <div class="character-count mt-1">
                                <span id="meta-title-count">0</span>/60 characters (recommended)
                            </div>
                            <?php $__errorArgs = ['meta_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Meta Description -->
                        <div class="mb-3">
                            <label for="meta_description" class="form-label">Meta Description</label>
                            <textarea class="form-control <?php $__errorArgs = ['meta_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                      id="meta_description" name="meta_description" rows="3"
                                      maxlength="160"><?php echo e(old('meta_description', $post->meta_description)); ?></textarea>
                            <div class="character-count mt-1">
                                <span id="meta-description-count">0</span>/160 characters (recommended)
                            </div>
                            <?php $__errorArgs = ['meta_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Focus Keywords -->
                        <div class="mb-3">
                            <label for="focus_keywords" class="form-label">Focus Keywords</label>
                            <input type="text" class="form-control <?php $__errorArgs = ['focus_keywords'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="focus_keywords" name="focus_keywords"
                                   value="<?php echo e(old('focus_keywords', $post->focus_keywords)); ?>"
                                   placeholder="software development, web development, Kenya">
                            <small class="form-text text-muted">Separate keywords with commas</small>
                            <?php $__errorArgs = ['focus_keywords'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Publish Settings -->
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Publish Settings</h6>
                    </div>
                    <div class="card-body">
                        <!-- Status -->
                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="status" name="status">
                                <option value="draft" <?php echo e(old('status', $post->status) == 'draft' ? 'selected' : ''); ?>>Draft</option>
                                <option value="published" <?php echo e(old('status', $post->status) == 'published' ? 'selected' : ''); ?>>Published</option>
                                <option value="archived" <?php echo e(old('status', $post->status) == 'archived' ? 'selected' : ''); ?>>Archived</option>
                            </select>
                            <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Published At -->
                        <div class="mb-3">
                            <label for="published_at" class="form-label">Publish Date</label>
                            <input type="datetime-local" class="form-control <?php $__errorArgs = ['published_at'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="published_at" name="published_at"
                                   value="<?php echo e(old('published_at', $post->published_at ? $post->published_at->format('Y-m-d\TH:i') : '')); ?>">
                            <?php $__errorArgs = ['published_at'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Author -->
                        <div class="mb-3">
                            <label for="author" class="form-label">Author</label>
                            <input type="text" class="form-control <?php $__errorArgs = ['author'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="author" name="author" value="<?php echo e(old('author', $post->author ?: auth()->user()->name)); ?>">
                            <?php $__errorArgs = ['author'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Reading Time -->
                        <div class="mb-3">
                            <label for="reading_time" class="form-label">Reading Time (minutes)</label>
                            <input type="number" class="form-control <?php $__errorArgs = ['reading_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="reading_time" name="reading_time" min="1"
                                   value="<?php echo e(old('reading_time', $post->reading_time)); ?>">
                            <small class="form-text text-muted">Leave empty to auto-calculate</small>
                            <?php $__errorArgs = ['reading_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                <?php echo e(isset($post->id) ? 'Update Post' : 'Create Post'); ?>

                            </button>
                            <?php if(isset($post->id)): ?>
                                <a href="<?php echo e(route('blog.show', $post->slug)); ?>" target="_blank" class="btn btn-outline-info">
                                    <i class="fas fa-eye me-2"></i>Preview Post
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Featured Image -->
                <div class="card shadow mt-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-info">Featured Image</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <input type="file" class="form-control <?php $__errorArgs = ['featured_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="featured_image" name="featured_image" accept="image/*">
                            <?php $__errorArgs = ['featured_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <?php if($post->featured_image): ?>
                            <div class="current-image mb-3">
                                <label class="form-label">Current Image:</label>
                                <div>
                                    <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>"
                                         alt="Featured Image" class="preview-image">
                                </div>
                                <div class="form-check mt-2">
                                    <input type="checkbox" class="form-check-input" id="remove_image" name="remove_image" value="1">
                                    <label class="form-check-label" for="remove_image">
                                        Remove current image
                                    </label>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Categories -->
                <div class="card shadow mt-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-warning">Categories</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <select class="form-select <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="category" name="category">
                                <option value="">Select Category</option>
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($category); ?>" <?php echo e(old('category', $post->category) == $category ? 'selected' : ''); ?>>
                                        <?php echo e(ucfirst($category)); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Tags -->
                <div class="card shadow mt-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-success">Tags</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="tag-input" id="tagInput">
                                <?php if($post->tags && is_array($post->tags)): ?>
                                    <?php $__currentLoopData = $post->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="tag-item">
                                            <?php echo e(trim($tag)); ?>

                                            <span class="remove-tag">&times;</span>
                                        </span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php elseif($post->tags && is_string($post->tags)): ?>
                                    <?php $__currentLoopData = explode(',', $post->tags); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="tag-item">
                                            <?php echo e(trim($tag)); ?>

                                            <span class="remove-tag">&times;</span>
                                        </span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </div>
                            <input type="hidden" name="tags" id="tagsHidden" value="<?php echo e(old('tags', is_array($post->tags) ? implode(', ', $post->tags) : $post->tags)); ?>">
                            <input type="text" class="form-control mt-2" id="tagInputField"
                                   placeholder="Type a tag and press Enter">
                            <small class="form-text text-muted">Press Enter to add tags</small>
                        </div>

                        <div class="available-tags">
                            <label class="form-label">Suggested Tags:</label>
                            <div>
                                <?php $__currentLoopData = $tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="badge bg-light text-dark me-1 mb-1 suggested-tag"
                                          style="cursor: pointer;"><?php echo e($tag); ?></span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Character counting
    function updateCharacterCount(inputId, countId, maxLength, recommendedLength = null) {
        const input = $('#' + inputId);
        const counter = $('#' + countId);

        function updateCount() {
            const length = input.val().length;
            counter.text(length);

            // Remove existing classes
            counter.removeClass('warning danger');

            if (recommendedLength) {
                if (length > recommendedLength) {
                    counter.addClass('danger');
                } else if (length > recommendedLength * 0.8) {
                    counter.addClass('warning');
                }
            } else {
                if (length > maxLength * 0.9) {
                    counter.addClass('danger');
                } else if (length > maxLength * 0.7) {
                    counter.addClass('warning');
                }
            }
        }

        input.on('input', updateCount);
        updateCount(); // Initial count
    }

    // Initialize character counters
    updateCharacterCount('title', 'title-count', 255);
    updateCharacterCount('excerpt', 'excerpt-count', 500);
    updateCharacterCount('meta_title', 'meta-title-count', 60, 60);
    updateCharacterCount('meta_description', 'meta-description-count', 160, 160);

    // Auto-generate slug from title
    $('#title').on('input', function() {
        if ($('#slug').val() === '') {
            const slug = $(this).val()
                .toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            $('#slug').val(slug);
        }
    });

    // Tag management
    let tags = [];

    // Initialize existing tags
    const existingTags = $('#tagsHidden').val();
    if (existingTags) {
        tags = existingTags.split(',').map(tag => tag.trim()).filter(tag => tag);
        updateTagDisplay();
    }

    // Add tag on Enter
    $('#tagInputField').on('keypress', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            const tag = $(this).val().trim();
            if (tag && !tags.includes(tag)) {
                tags.push(tag);
                updateTagDisplay();
                $(this).val('');
            }
        }
    });

    // Remove tag
    $(document).on('click', '.remove-tag', function() {
        const tagText = $(this).parent().text().replace('×', '').trim();
        tags = tags.filter(tag => tag !== tagText);
        updateTagDisplay();
    });

    // Add suggested tag
    $(document).on('click', '.suggested-tag', function() {
        const tag = $(this).text().trim();
        if (!tags.includes(tag)) {
            tags.push(tag);
            updateTagDisplay();
        }
    });

    function updateTagDisplay() {
        const tagContainer = $('#tagInput');
        tagContainer.empty();

        tags.forEach(tag => {
            tagContainer.append(`
                <span class="tag-item">
                    ${tag}
                    <span class="remove-tag">&times;</span>
                </span>
            `);
        });

        $('#tagsHidden').val(tags.join(', '));
    }

    // Auto-calculate reading time
    $('#content').on('input', function() {
        if ($('#reading_time').val() === '') {
            const content = $(this).val();
            const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;
            const readingTime = Math.max(1, Math.ceil(wordCount / 200)); // 200 words per minute
            $('#reading_time').val(readingTime);
        }
    });

    // Auto-generate meta fields if empty
    $('#title').on('input', function() {
        const title = $(this).val();
        if ($('#meta_title').val() === '') {
            $('#meta_title').val(title.substring(0, 60));
        }
    });

    $('#excerpt').on('input', function() {
        const excerpt = $(this).val();
        if ($('#meta_description').val() === '') {
            $('#meta_description').val(excerpt.substring(0, 160));
        }
    });

    // Form validation
    $('#blogForm').on('submit', function(e) {
        let isValid = true;

        // Check required fields
        if ($('#title').val().trim() === '') {
            alert('Title is required');
            isValid = false;
        }

        if ($('#content').val().trim() === '') {
            alert('Content is required');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
        }
    });

    // Preview image
    $('#featured_image').on('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // Remove existing preview
                $('.image-preview').remove();

                // Add new preview
                $('#featured_image').after(`
                    <div class="image-preview mt-2">
                        <img src="${e.target.result}" alt="Preview" class="preview-image">
                    </div>
                `);
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\Grandtek\resources\views/admin/blog/form.blade.php ENDPATH**/ ?>