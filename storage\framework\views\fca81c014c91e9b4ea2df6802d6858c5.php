<?php $__env->startSection('title', 'Our Systems - Innovative Solutions'); ?>
<?php $__env->startSection('meta_description', 'Explore our comprehensive collection of innovative systems and applications. View details, ratings, and live demos of our cutting-edge solutions.'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid py-5">
    <div class="container">
        <!-- Page Header -->
        <div class="text-center mx-auto pb-5 wow fadeIn" data-wow-delay=".3s" style="max-width: 800px;">
            <h1 class="display-4 mb-3">Our Systems</h1>
            <p class="lead text-muted">
                Discover our comprehensive collection of innovative systems and applications. 
                Each solution is crafted with cutting-edge technology to solve real-world problems.
            </p>
        </div>

        <!-- Systems Grid -->
        <?php if($systems->count() > 0): ?>
            <div class="row g-4">
                <?php $__currentLoopData = $systems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $system): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-6 col-xl-4 wow fadeIn" data-wow-delay="<?php echo e(.3 + ($index * 0.1)); ?>s">
                        <div class="system-item position-relative bg-light rounded h-100">
                            <!-- System Image -->
                            <div class="system-image position-relative overflow-hidden rounded-top">
                                <?php if($system->system_image_url): ?>
                                    <img src="<?php echo e($system->system_image_url); ?>" class="img-fluid w-100" alt="<?php echo e($system->title); ?>" 
                                         style="height: 250px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-primary bg-opacity-10 d-flex align-items-center justify-content-center" 
                                         style="height: 250px;">
                                        <i class="ri-computer-line display-4 text-primary"></i>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- System Category Badge -->
                                <?php if($system->category): ?>
                                    <span class="position-absolute px-3 py-2 bg-primary text-white rounded" 
                                          style="top: 15px; right: 15px; font-size: 0.85rem;">
                                        <?php echo e($system->category); ?>

                                    </span>
                                <?php endif; ?>

                                <!-- System Status Badge -->
                                <div class="position-absolute" style="top: 15px; left: 15px;">
                                    <?php switch($system->status):
                                        case ('active'): ?>
                                            <span class="badge bg-success">Live</span>
                                            <?php break; ?>
                                        <?php case ('development'): ?>
                                            <span class="badge bg-warning">In Development</span>
                                            <?php break; ?>
                                        <?php case ('maintenance'): ?>
                                            <span class="badge bg-info">Maintenance</span>
                                            <?php break; ?>
                                    <?php endswitch; ?>
                                </div>

                                <!-- Hover Overlay -->
                                <div class="system-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
                                     style="background: rgba(0,0,0,0.7); opacity: 0; transition: opacity 0.3s;">
                                    <div class="text-center">
                                        <?php if($system->system_url): ?>
                                            <a href="<?php echo e($system->system_url); ?>" target="_blank" 
                                               class="btn btn-primary btn-sm me-2 mb-2">
                                                <i class="ri-external-link-line me-1"></i>Live Demo
                                            </a>
                                        <?php endif; ?>
                                        <?php if($system->github_url): ?>
                                            <a href="<?php echo e($system->github_url); ?>" target="_blank" 
                                               class="btn btn-secondary btn-sm me-2 mb-2">
                                                <i class="ri-github-line me-1"></i>GitHub
                                            </a>
                                        <?php endif; ?>
                                        <a href="<?php echo e(route('system.detail', $system->slug)); ?>" 
                                           class="btn btn-success btn-sm mb-2">
                                            <i class="ri-eye-line me-1"></i>View Details
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- System Content -->
                            <div class="system-content p-4">
                                <!-- System Title & Version -->
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h5 class="system-title mb-0"><?php echo e($system->title); ?></h5>
                                    <?php if($system->version): ?>
                                        <span class="badge bg-secondary-subtle text-secondary">v<?php echo e($system->version); ?></span>
                                    <?php endif; ?>
                                </div>

                                <!-- System Description -->
                                <p class="text-muted mb-3" style="font-size: 0.9rem; line-height: 1.5;">
                                    <?php echo e($system->short_description ?: Str::limit($system->description, 120)); ?>

                                </p>

                                <!-- Technologies Used -->
                                <?php if($system->technologies && count($system->technologies) > 0): ?>
                                    <div class="mb-3">
                                        <div class="d-flex flex-wrap gap-1">
                                            <?php $__currentLoopData = array_slice($system->technologies, 0, 4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tech): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="badge bg-primary-subtle text-primary" style="font-size: 0.75rem;">
                                                    <?php echo e($tech); ?>

                                                </span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php if(count($system->technologies) > 4): ?>
                                                <span class="badge bg-light text-muted" style="font-size: 0.75rem;">
                                                    +<?php echo e(count($system->technologies) - 4); ?> more
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Rating & Launch Date -->
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="rating">
                                        <?php if($system->total_ratings > 0): ?>
                                            <div class="d-flex align-items-center">
                                                <div class="stars me-2">
                                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                                        <?php if($i <= floor($system->average_rating)): ?>
                                                            <i class="fas fa-star text-warning"></i>
                                                        <?php elseif($i - 0.5 <= $system->average_rating): ?>
                                                            <i class="fas fa-star-half-alt text-warning"></i>
                                                        <?php else: ?>
                                                            <i class="far fa-star text-muted"></i>
                                                        <?php endif; ?>
                                                    <?php endfor; ?>
                                                </div>
                                                <small class="text-muted">
                                                    <?php echo e(number_format($system->average_rating, 1)); ?> (<?php echo e($system->total_ratings); ?>)
                                                </small>
                                            </div>
                                        <?php else: ?>
                                            <small class="text-muted">No ratings yet</small>
                                        <?php endif; ?>
                                    </div>
                                    <?php if($system->launch_date): ?>
                                        <small class="text-muted">
                                            <i class="ri-calendar-line me-1"></i>
                                            <?php echo e(\Carbon\Carbon::parse($system->launch_date)->format('M Y')); ?>

                                        </small>
                                    <?php endif; ?>
                                </div>

                                <!-- Action Buttons -->
                                <div class="d-flex gap-2">
                                    <a href="<?php echo e(route('system.detail', $system->slug)); ?>" 
                                       class="btn btn-primary btn-sm flex-fill">
                                        <i class="ri-eye-line me-1"></i>View & Rate
                                    </a>
                                    <?php if($system->system_url): ?>
                                        <a href="<?php echo e($system->system_url); ?>" target="_blank" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="ri-external-link-line"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Pagination -->
            <?php if($systems->hasPages()): ?>
                <div class="d-flex justify-content-center mt-5">
                    <?php echo e($systems->links()); ?>

                </div>
            <?php endif; ?>

        <?php else: ?>
            <!-- Empty State -->
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="ri-computer-line display-1 text-muted"></i>
                </div>
                <h3 class="text-muted">No Systems Available</h3>
                <p class="text-muted mb-4">We're working on exciting new systems. Check back soon!</p>
                <a href="<?php echo e(route('home')); ?>" class="btn btn-primary">
                    <i class="ri-home-line me-1"></i>Back to Home
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .system-item {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: 1px solid #e9ecef;
    }
    
    .system-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .system-item:hover .system-overlay {
        opacity: 1 !important;
    }
    
    .system-image {
        position: relative;
        overflow: hidden;
    }
    
    .system-title {
        color: #2c3e50;
        font-weight: 600;
    }
    
    .stars i {
        font-size: 0.9rem;
    }
    
    .badge {
        font-weight: 500;
    }
    
    .system-content {
        background: white;
    }
    
    @media (max-width: 768px) {
        .system-item {
            margin-bottom: 2rem;
        }
        
        .system-overlay {
            opacity: 1 !important;
            background: rgba(0,0,0,0.5) !important;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\Grandtek\resources\views/frontend/systems/index.blade.php ENDPATH**/ ?>