@extends('backend.layouts.app')

@section('title', 'View System')

@section('content')
<!-- <PERSON> Header -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">System Details</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('systems.index') }}">Systems</a></li>
                    <li class="breadcrumb-item active">{{ $system->title }}</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <a href="{{ route('systems.index') }}" class="btn btn-secondary">
                    <i class="ri-arrow-left-line me-1"></i>Back to Systems
                </a>
            </div>
            <div>
                <a href="{{ route('systems.edit', $system->id) }}" class="btn btn-primary">
                    <i class="ri-edit-line me-1"></i>Edit System
                </a>
                @if($system->status === 'active')
                    <a href="{{ route('system.detail', $system->slug) }}" class="btn btn-outline-info" target="_blank">
                        <i class="ri-external-link-line me-1"></i>View Frontend
                    </a>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">System Information</h4>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Title:</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        {{ $system->title }}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Slug:</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        <code>{{ $system->slug }}</code>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Status:</h6>
                    </div>
                    <div class="col-sm-9">
                        @php
                            $statusColors = [
                                'active' => 'success',
                                'inactive' => 'secondary',
                                'development' => 'warning',
                                'maintenance' => 'info'
                            ];
                        @endphp
                        <span class="badge bg-{{ $statusColors[$system->status] ?? 'secondary' }}">
                            {{ ucfirst(str_replace('_', ' ', $system->status)) }}
                        </span>
                    </div>
                </div>

                @if($system->category)
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Category:</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        {{ $system->category }}
                    </div>
                </div>
                @endif

                @if($system->version)
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Version:</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        {{ $system->version }}
                    </div>
                </div>
                @endif

                @if($system->system_url)
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">System URL:</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        <a href="{{ $system->system_url }}" target="_blank" class="text-decoration-none">
                            {{ $system->system_url }} <i class="ri-external-link-line"></i>
                        </a>
                    </div>
                </div>
                @endif

                @if($system->documentation_url)
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Documentation:</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        <a href="{{ $system->documentation_url }}" target="_blank" class="text-decoration-none">
                            {{ $system->documentation_url }} <i class="ri-external-link-line"></i>
                        </a>
                    </div>
                </div>
                @endif

                @if($system->launch_date)
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Launch Date:</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        {{ $system->launch_date->format('M d, Y') }}
                    </div>
                </div>
                @endif

                @if($system->technologies && count($system->technologies) > 0)
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Technologies:</h6>
                    </div>
                    <div class="col-sm-9">
                        @foreach($system->technologies as $tech)
                            <span class="badge bg-primary me-1 mb-1">{{ $tech }}</span>
                        @endforeach
                    </div>
                </div>
                @endif

                @if($system->features && count($system->features) > 0)
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Key Features:</h6>
                    </div>
                    <div class="col-sm-9">
                        @foreach($system->features as $feature)
                            <span class="badge bg-success me-1 mb-1">{{ $feature }}</span>
                        @endforeach
                    </div>
                </div>
                @endif

                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Featured:</h6>
                    </div>
                    <div class="col-sm-9">
                        @if($system->featured)
                            <span class="badge bg-success">Yes</span>
                        @else
                            <span class="badge bg-secondary">No</span>
                        @endif
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Rating:</h6>
                    </div>
                    <div class="col-sm-9">
                        @if($system->average_rating > 0)
                            <div class="d-flex align-items-center">
                                <div class="rating-stars me-2">
                                    @for($i = 1; $i <= 5; $i++)
                                        @if($i <= $system->average_rating)
                                            <i class="ri-star-fill text-warning"></i>
                                        @else
                                            <i class="ri-star-line text-muted"></i>
                                        @endif
                                    @endfor
                                </div>
                                <span class="text-muted">{{ number_format($system->average_rating, 1) }} ({{ $system->total_ratings }} {{ Str::plural('rating', $system->total_ratings) }})</span>
                            </div>
                        @else
                            <span class="text-muted">No ratings yet</span>
                        @endif
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Views:</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        {{ number_format($system->views_count) }} {{ Str::plural('view', $system->views_count) }}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Created:</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        {{ $system->created_at->format('M d, Y \a\t g:i A') }}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3">
                        <h6 class="mb-0">Last Updated:</h6>
                    </div>
                    <div class="col-sm-9 text-secondary">
                        {{ $system->updated_at->format('M d, Y \a\t g:i A') }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Descriptions -->
        <div class="card mt-4">
            <div class="card-header">
                <h4 class="card-title mb-0">Descriptions</h4>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h6>Description:</h6>
                    <p class="text-muted">{{ $system->description }}</p>
                </div>

                @if($system->short_description)
                <div class="mb-4">
                    <h6>Short Description:</h6>
                    <p class="text-muted">{{ $system->short_description }}</p>
                </div>
                @endif

                @if($system->full_description)
                <div class="mb-4">
                    <h6>Full Description:</h6>
                    <div class="text-muted">{!! nl2br(e($system->full_description)) !!}</div>
                </div>
                @endif

                @if($system->changelog)
                <div class="mb-4">
                    <h6>Changelog:</h6>
                    <div class="text-muted">{!! nl2br(e($system->changelog)) !!}</div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- System Image -->
        @if($system->system_image_url)
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Main Image</h5>
            </div>
            <div class="card-body">
                <img src="{{ $system->system_image_url }}" class="img-fluid rounded" alt="{{ $system->title }}">
            </div>
        </div>
        @endif

        <!-- Gallery Images -->
        @if($system->gallery_images && count($system->gallery_images) > 0)
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Gallery Images</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($system->gallery_images as $image)
                        <div class="col-6 mb-3">
                            <img src="{{ Storage::url($image) }}" class="img-fluid rounded" alt="Gallery Image">
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
        @endif

        <!-- SEO Information -->
        @if($system->meta_title || $system->meta_description || $system->meta_keywords)
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">SEO Information</h5>
            </div>
            <div class="card-body">
                @if($system->meta_title)
                <div class="mb-3">
                    <h6>Meta Title:</h6>
                    <p class="text-muted small">{{ $system->meta_title }}</p>
                </div>
                @endif

                @if($system->meta_description)
                <div class="mb-3">
                    <h6>Meta Description:</h6>
                    <p class="text-muted small">{{ $system->meta_description }}</p>
                </div>
                @endif

                @if($system->meta_keywords)
                <div class="mb-3">
                    <h6>Meta Keywords:</h6>
                    <p class="text-muted small">{{ $system->meta_keywords }}</p>
                </div>
                @endif
            </div>
        </div>
        @endif

        <!-- Recent Ratings -->
        @if($system->ratings && $system->ratings->count() > 0)
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Recent Ratings</h5>
            </div>
            <div class="card-body">
                @foreach($system->ratings->take(3) as $rating)
                    <div class="mb-3 pb-3 border-bottom">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="rating-stars mb-1">
                                    @for($i = 1; $i <= 5; $i++)
                                        @if($i <= $rating->rating)
                                            <i class="ri-star-fill text-warning"></i>
                                        @else
                                            <i class="ri-star-line text-muted"></i>
                                        @endif
                                    @endfor
                                </div>
                                <small class="text-muted">{{ $rating->created_at->diffForHumans() }}</small>
                            </div>
                            <span class="badge bg-{{ $rating->status === 'active' ? 'success' : 'secondary' }}">
                                {{ ucfirst($rating->status) }}
                            </span>
                        </div>
                        @if($rating->comment)
                            <p class="text-muted small mt-2 mb-0">{{ $rating->comment }}</p>
                        @endif
                    </div>
                @endforeach
                @if($system->ratings->count() > 3)
                    <div class="text-center">
                        <a href="{{ route('system.detail', $system->slug) }}" class="btn btn-sm btn-outline-primary">
                            View All Ratings
                        </a>
                    </div>
                @endif
            </div>
        </div>
        @endif
    </div>
</div>
@endsection
