@extends('backend.layouts.app')

@section('title', 'Project Ratings - ' . $project->title)

@section('content')
<!-- Page Header -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Project Ratings</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('projects.index') }}">Projects</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('projects.show', $project->id) }}">{{ $project->title }}</a></li>
                    <li class="breadcrumb-item active">Ratings</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Project Info Card -->
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="card-title mb-2">{{ $project->title }}</h5>
                        <p class="text-muted mb-0">{{ $project->description }}</p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="rating-summary">
                            @if($project->total_ratings > 0)
                                <div class="d-flex align-items-center justify-content-md-end">
                                    <span class="rating-number me-2">{{ number_format($project->average_rating, 1) }}</span>
                                    <div class="stars me-2">
                                        @for($i = 1; $i <= 5; $i++)
                                            @if($i <= floor($project->average_rating))
                                                <i class="fas fa-star text-warning"></i>
                                            @elseif($i <= ceil($project->average_rating) && $project->average_rating > floor($project->average_rating))
                                                <i class="fas fa-star-half-alt text-warning"></i>
                                            @else
                                                <i class="far fa-star text-muted"></i>
                                            @endif
                                        @endfor
                                    </div>
                                    <span class="text-muted">({{ $project->total_ratings }} {{ Str::plural('review', $project->total_ratings) }})</span>
                                </div>
                            @else
                                <span class="text-muted">No ratings yet</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@if($project->total_ratings > 0)
<!-- Rating Distribution -->
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Rating Distribution</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach(array_reverse($ratingDistribution, true) as $star => $data)
                        <div class="col-md-2 col-6 mb-3">
                            <div class="text-center">
                                <div class="rating-bar-vertical">
                                    <div class="rating-label mb-2">
                                        {{ $star }} <i class="fas fa-star text-warning"></i>
                                    </div>
                                    <div class="progress vertical-progress mb-2" style="height: 60px;">
                                        <div class="progress-bar bg-warning" style="height: {{ $data['percentage'] }}%"></div>
                                    </div>
                                    <div class="rating-count">
                                        <strong>{{ $data['count'] }}</strong>
                                        <small class="text-muted d-block">{{ $data['percentage'] }}%</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Ratings List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">All Ratings ({{ $ratings->total() }})</h5>
                <div class="card-tools">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="filterRatings('all')">All</button>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="filterRatings('active')">Active</button>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="filterRatings('featured')">Featured</button>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="filterRatings('verified')">Verified</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                @if($ratings->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Reviewer</th>
                                    <th>Rating</th>
                                    <th>Review</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($ratings as $rating)
                                    <tr data-rating-id="{{ $rating->id }}" 
                                        data-status="{{ $rating->status }}"
                                        data-featured="{{ $rating->is_featured ? 'true' : 'false' }}"
                                        data-verified="{{ $rating->is_verified ? 'true' : 'false' }}">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <div class="avatar-title bg-light text-primary rounded-circle">
                                                        {{ strtoupper(substr($rating->display_name, 0, 1)) }}
                                                    </div>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ $rating->display_name }}</h6>
                                                    <small class="text-muted">{{ $rating->display_email }}</small>
                                                    @if($rating->is_registered_user)
                                                        <span class="badge bg-info ms-1">Registered</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="rating-display">
                                                <span class="rating-number me-1">{{ $rating->rating }}</span>
                                                <div class="stars">
                                                    @for($i = 1; $i <= 5; $i++)
                                                        @if($i <= $rating->rating)
                                                            <i class="fas fa-star text-warning"></i>
                                                        @else
                                                            <i class="far fa-star text-muted"></i>
                                                        @endif
                                                    @endfor
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @if($rating->review)
                                                <div class="review-text">
                                                    {{ Str::limit($rating->review, 100) }}
                                                    @if(strlen($rating->review) > 100)
                                                        <a href="#" class="text-primary" onclick="showFullReview('{{ $rating->id }}')">Read more</a>
                                                    @endif
                                                </div>
                                                <div class="full-review d-none" id="full-review-{{ $rating->id }}">
                                                    {{ $rating->review }}
                                                </div>
                                            @else
                                                <span class="text-muted">No review text</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="status-badges">
                                                <span class="badge bg-{{ $rating->status === 'active' ? 'success' : 'secondary' }}">
                                                    {{ ucfirst($rating->status) }}
                                                </span>
                                                @if($rating->is_featured)
                                                    <span class="badge bg-warning">Featured</span>
                                                @endif
                                                @if($rating->is_verified)
                                                    <span class="badge bg-info">Verified</span>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <div class="date-info">
                                                <div>{{ $rating->created_at->format('M d, Y') }}</div>
                                                <small class="text-muted">{{ $rating->created_at->format('h:i A') }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-warning" 
                                                        onclick="toggleFeatured({{ $rating->id }})"
                                                        title="Toggle Featured">
                                                    <i class="fas fa-star"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-info" 
                                                        onclick="toggleVerified({{ $rating->id }})"
                                                        title="Toggle Verified">
                                                    <i class="fas fa-check-circle"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" 
                                                        onclick="toggleStatus({{ $rating->id }})"
                                                        title="Toggle Status">
                                                    <i class="fas fa-toggle-on"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="deleteRating({{ $rating->id }})"
                                                        title="Delete Rating">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($ratings->hasPages())
                        <div class="d-flex justify-content-center mt-4">
                            {{ $ratings->links() }}
                        </div>
                    @endif
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No ratings found</h5>
                        <p class="text-muted">This project hasn't received any ratings yet.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.rating-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ffc107;
}

.vertical-progress {
    width: 20px;
    margin: 0 auto;
}

.rating-bar-vertical {
    text-align: center;
}

.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.review-text {
    max-width: 300px;
    word-wrap: break-word;
}

.status-badges .badge {
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
}

.date-info {
    min-width: 100px;
}

.btn-group .btn {
    margin-right: 0.25rem;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-group .btn {
        padding: 0.25rem 0.5rem;
    }

    .review-text {
        max-width: 200px;
    }
}
</style>
@endpush

@push('scripts')
<script>
function filterRatings(filter) {
    const rows = document.querySelectorAll('tbody tr[data-rating-id]');

    rows.forEach(row => {
        let show = false;

        switch(filter) {
            case 'all':
                show = true;
                break;
            case 'active':
                show = row.dataset.status === 'active';
                break;
            case 'featured':
                show = row.dataset.featured === 'true';
                break;
            case 'verified':
                show = row.dataset.verified === 'true';
                break;
        }

        row.style.display = show ? '' : 'none';
    });

    // Update button states
    document.querySelectorAll('.card-tools .btn').forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-outline-primary');
    });

    event.target.classList.remove('btn-outline-primary');
    event.target.classList.add('btn-primary');
}

function showFullReview(ratingId) {
    const fullReview = document.getElementById('full-review-' + ratingId);
    const reviewText = fullReview.previousElementSibling;

    if (fullReview.classList.contains('d-none')) {
        fullReview.classList.remove('d-none');
        reviewText.style.display = 'none';
    } else {
        fullReview.classList.add('d-none');
        reviewText.style.display = 'block';
    }
}

function toggleFeatured(ratingId) {
    makeRequest('toggle-featured', ratingId, 'featured status');
}

function toggleVerified(ratingId) {
    makeRequest('toggle-verified', ratingId, 'verified status');
}

function toggleStatus(ratingId) {
    makeRequest('toggle-status', ratingId, 'status');
}

function deleteRating(ratingId) {
    if (confirm('Are you sure you want to delete this rating? This action cannot be undone.')) {
        makeRequest('delete', ratingId, 'rating', 'DELETE');
    }
}

function makeRequest(action, ratingId, actionName, method = 'POST') {
    const url = `{{ route('projects.ratings.toggle-featured', [$project->id, ':rating']) }}`.replace(':rating', ratingId).replace('toggle-featured', action);

    fetch(url, {
        method: method,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (action === 'delete') {
                // Remove the row
                document.querySelector(`tr[data-rating-id="${ratingId}"]`).remove();
            } else {
                // Update the row data and badges
                updateRowData(ratingId, data);
            }

            // Show success message
            showAlert('success', data.message);
        } else {
            showAlert('error', data.message || `Failed to update ${actionName}`);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', `An error occurred while updating ${actionName}`);
    });
}

function updateRowData(ratingId, data) {
    const row = document.querySelector(`tr[data-rating-id="${ratingId}"]`);

    if (data.hasOwnProperty('is_featured')) {
        row.dataset.featured = data.is_featured ? 'true' : 'false';
    }

    if (data.hasOwnProperty('is_verified')) {
        row.dataset.verified = data.is_verified ? 'true' : 'false';
    }

    if (data.hasOwnProperty('status')) {
        row.dataset.status = data.status;
    }

    // Update badges
    updateStatusBadges(row, data);
}

function updateStatusBadges(row, data) {
    const statusCell = row.querySelector('.status-badges');
    let badges = '';

    const status = data.status || row.dataset.status;
    const isFeatured = data.hasOwnProperty('is_featured') ? data.is_featured : row.dataset.featured === 'true';
    const isVerified = data.hasOwnProperty('is_verified') ? data.is_verified : row.dataset.verified === 'true';

    badges += `<span class="badge bg-${status === 'active' ? 'success' : 'secondary'}">${status.charAt(0).toUpperCase() + status.slice(1)}</span>`;

    if (isFeatured) {
        badges += '<span class="badge bg-warning">Featured</span>';
    }

    if (isVerified) {
        badges += '<span class="badge bg-info">Verified</span>';
    }

    statusCell.innerHTML = badges;
}

function showAlert(type, message) {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at top of content
    const content = document.querySelector('.row').parentNode;
    content.insertBefore(alertDiv, content.firstChild);

    // Auto dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
@endpush
