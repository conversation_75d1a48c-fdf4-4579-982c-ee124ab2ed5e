@extends('frontend.layouts.app')

@section('title', $service->name . ' - Grandtek Services')

@section('content')
<!-- Page Header -->
<div class="container-fluid page-header py-5">
    <div class="container text-center py-5">
        <h1 class="display-4 text-white mb-4 animated slideInDown">{{ $service->name }}</h1>
        <nav aria-label="breadcrumb animated slideInDown">
            <ol class="breadcrumb justify-content-center mb-0">
                <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                <li class="breadcrumb-item"><a href="{{ route('services.all') }}">Services</a></li>
                <li class="breadcrumb-item text-white" aria-current="page">{{ $service->name }}</li>
            </ol>
        </nav>
    </div>
</div>

<!-- Service Detail Section -->
<div class="container-fluid py-5">
    <div class="container">
        <div class="row g-5">
            <!-- Service Content -->
            <div class="col-lg-8">
                <!-- Service Image -->
                @if($service->service_image_url)
                    <div class="service-main-image mb-4 wow fadeIn" data-wow-delay=".3s">
                        <img src="{{ $service->service_image_url }}" class="img-fluid w-100 rounded" 
                             alt="{{ $service->name }}" style="height: 400px; object-fit: cover;">
                    </div>
                @endif

                <!-- Service Description -->
                <div class="service-description wow fadeIn" data-wow-delay=".5s">
                    <h2 class="mb-4">About This Service</h2>
                    <div class="service-content">
                        {!! nl2br(e($service->description)) !!}
                    </div>
                </div>

                <!-- Additional Information -->
                @if($service->short_description && $service->short_description !== $service->description)
                    <div class="service-summary mt-5 wow fadeIn" data-wow-delay=".7s">
                        <div class="bg-light p-4 rounded">
                            <h5 class="text-primary mb-3">
                                <i class="ri-information-line me-2"></i>Service Summary
                            </h5>
                            <p class="mb-0">{{ $service->short_description }}</p>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Service Info Sidebar -->
            <div class="col-lg-4">
                <div class="service-info-sidebar">
                    <!-- Service Details Card -->
                    <div class="card mb-4 wow fadeIn" data-wow-delay=".3s">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="ri-service-line me-2"></i>Service Details</h5>
                        </div>
                        <div class="card-body">
                            <div class="detail-item mb-3">
                                <strong><i class="ri-bookmark-line me-2 text-primary"></i>Service Name:</strong>
                                <span class="ms-2">{{ $service->name }}</span>
                            </div>

                            @if($service->icon_class)
                                <div class="detail-item mb-3">
                                    <strong><i class="ri-palette-line me-2 text-primary"></i>Service Icon:</strong>
                                    <span class="ms-2"><i class="{{ $service->icon_class }} text-primary"></i></span>
                                </div>
                            @endif

                            <div class="detail-item mb-3">
                                <strong><i class="ri-calendar-line me-2 text-primary"></i>Added:</strong>
                                <span class="ms-2">{{ $service->created_at->format('M d, Y') }}</span>
                            </div>

                            <div class="detail-item">
                                <strong><i class="ri-refresh-line me-2 text-primary"></i>Last Updated:</strong>
                                <span class="ms-2">{{ $service->updated_at->format('M d, Y') }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Card -->
                    <div class="card mb-4 wow fadeIn" data-wow-delay=".5s">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="ri-phone-line me-2"></i>Get This Service</h5>
                        </div>
                        <div class="card-body text-center">
                            <p class="mb-3">Interested in this service? Contact us for more information and pricing.</p>
                            <a href="{{ route('home') }}#contact" class="btn btn-success btn-lg w-100 mb-2">
                                <i class="ri-mail-line me-2"></i>Contact Us
                            </a>
                            <a href="tel:+254700000000" class="btn btn-outline-success w-100">
                                <i class="ri-phone-line me-2"></i>Call Now
                            </a>
                        </div>
                    </div>

                    <!-- Related Services -->
                    <div class="card wow fadeIn" data-wow-delay=".7s">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="ri-links-line me-2"></i>Other Services</h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-3">Explore our other technology solutions:</p>
                            <a href="{{ route('services.all') }}" class="btn btn-info w-100">
                                <i class="ri-grid-line me-2"></i>View All Services
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Call to Action Section -->
<div class="container-fluid bg-primary py-5">
    <div class="container">
        <div class="row g-5 align-items-center">
            <div class="col-lg-8 wow fadeIn" data-wow-delay=".3s">
                <h2 class="text-white mb-3">Ready to Get Started with {{ $service->name }}?</h2>
                <p class="text-white mb-0">
                    Contact our team today to discuss how this service can benefit your business. 
                    We'll provide you with a customized solution that meets your specific needs.
                </p>
            </div>
            <div class="col-lg-4 text-lg-end wow fadeIn" data-wow-delay=".5s">
                <a href="{{ route('home') }}#contact" class="btn btn-light btn-lg px-5 py-3 rounded-pill">
                    <i class="ri-message-line me-2"></i>Get Quote
                </a>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.service-content {
    line-height: 1.8;
    font-size: 1.1rem;
}

.detail-item {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 0.75rem;
}

.detail-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.page-header {
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), 
                url('/frontend/img/carousel-1.jpg') center center/cover;
    min-height: 400px;
    display: flex;
    align-items: center;
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: white;
}

.card {
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}

.service-main-image img {
    cursor: pointer;
    transition: all 0.3s ease;
}

.service-main-image img:hover {
    transform: scale(1.02);
}
</style>
@endpush
